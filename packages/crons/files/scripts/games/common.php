<?php

function updateBaseData($database, $game, $bggGame)
{
    if (!$bggGame) {
        echo "Empty game data: " . $game["id"] . "\n";
        return;
    }

    try {
        $rank = null;

        foreach ($bggGame->statistics->ratings->ranks->rank as $rankEntry) {
            if ($rankEntry['name'] == 'boardgame') {
                $rank = $rankEntry['value']->__toString();
            }
        }

        foreach ($bggGame->name as $name) {
            if ($name['type'] == 'primary') {
                $title = $name['value']->__toString();
            }
        }

        $gameData = ['rank' => $rank,
            'average' => floatval($bggGame->statistics->ratings->average['value']->__toString()),
            'bayesaverage' => floatval($bggGame->statistics->ratings->bayesaverage['value']->__toString()),
            'averageweight' => floatval($bggGame->statistics->ratings->averageweight['value']->__toString()),
            'publishYear' => intval($bggGame->yearpublished['value']->__toString()),
            'players' => ['stats' => extractPlayerCountData($bggGame),
                'box' => isset($bggGame->minplayers['value']) ? ['min' => intval($bggGame->minplayers['value']->__toString()),
                    'max' => intval($bggGame->maxplayers['value']->__toString()),] : []],
            'playTime' => ['box' => isset($bggGame->minplaytime['value']) ? ['min' => intval($bggGame->minplaytime['value']->__toString()),
                'max' => intval($bggGame->maxplaytime['value']->__toString()),] : []],
            'age' => isset($bggGame->minage['value']) ? intval($bggGame->minage['value']->__toString()) : null,];

        if (!empty($title)) {
            $gameData['title'] = $title;
        }

        $database->update('games', ['bgg_info' => json_encode($gameData)], ['id' => $game['id']]);
        autoTags($database, $gameData, $game['id']);
    } catch (Exception $e) {
        echo "Failed to update game data: " . $game["id"] . "\n";
        var_dump($e);
    }
}

function getGameXml($gameId, $sleep)
{
    $bggGameDataFile = RequestFile::run('https://www.boardgamegeek.com/xmlapi2/thing?type=boardgame,boardgameexpansion,boardgameaccessory&stats=1&id=' . $gameId, $sleep);
    return $bggGameDataFile->item;
}

function extractPlayerCountData($bggGame)
{
    $voteType = ["Best"            => 1,
                 "Recommended"     => 2,
                 "Not Recommended" => 3,];

    $best = null;
    foreach ($bggGame->poll as $poll) {
        if ($poll['name'] == "suggested_numplayers") {
            foreach ($poll->results as $result) {

                $res = [];
                foreach ($result->result as $resultRow) {
                    $res[$resultRow['value']->__toString()] = intval($resultRow['numvotes']->__toString());
                }

                $maxT = "Not Recommended";
                $maxV = 0;

                foreach ($res as $type => $votes) {
                    if ($votes > $maxV) {
                        $maxT = $type;
                        $maxV = $votes;
                    }
                }

                $best[] = ['players' => intval($result['numplayers']->__toString()),
                           'votes'   => $maxV,
                           'status'  => $voteType[$maxT],];
            }

            return $best;
        }
    }

    return null;
}

function setGameUpdated($database, $game)
{
    $database->update('games', ['last_updated' => date('Y-m-d H:i:s', time())], ['id' => $game['id']]);
}

function improveTags($database, $gameId)
{
    $tags = $database->select("game2tag", ["tag_id"], ["game_id" => $gameId]);

    foreach ($tags as $tag) {
        $tagMergeList = $database->select("tag_merge", ["tag_to_id"], ["tag_from_id" => $tag['tag_id']]);

        foreach ($tagMergeList as $tagMerge) {
            // $database->insert("game2tag", ["game_id" => $gameId, "tag_id" => $tagMerge["tag_to_id"]]);
            $database->query("INSERT INTO `game2tag` SET `game_id` = " . $gameId . ", `tag_id`=" . $tagMerge["tag_to_id"] . " ON DUPLICATE KEY UPDATE `game_id` = " . $gameId);
        }
    }
}

function autoTags($database, $gameData, $gameId)
{
    $lengthMatrix = [['id' => 1525, 'from' => 0, 'till' => 30, 'threshold' => 10],
                     ['id' => 1526, 'from' => 31, 'till' => 60, 'threshold' => 30],
                     ['id' => 1527, 'from' => 61, 'till' => 180, 'threshold' => 60],
                     ['id' => 1528, 'from' => 181, 'till' => 100000000, 'threshold' => null]];

    $complexityMatrix = [['id' => 1529, 'from' => 0, 'till' => 1],
                         ['id' => 1530, 'from' => 1, 'till' => 2],
                         ['id' => 1531, 'from' => 2, 'till' => 3],
                         ['id' => 1532, 'from' => 3, 'till' => 4],
                         ['id' => 1533, 'from' => 4, 'till' => 5],];

    // clean up from existing values

    foreach ($lengthMatrix as $length) {
        $lenghtId[] = $length['id'];
    }

    foreach ($complexityMatrix as $complexity) {
        $complexityId[] = $complexity['id'];
    }

    $database->query("DELETE FROM `game2tag` WHERE `game_id` = " . $gameId . " AND tag_id IN (" . implode(",", array_merge($lenghtId, $complexityId)) . ")");

    // determine game length tag

    $gameMin = !empty($gameData['playTime']['box']) ? intval($gameData['playTime']['box']['min']) : null;
    $gameMax = !empty($gameData['playTime']['box']) ? intval($gameData['playTime']['box']['max']) : 0;

    $gameFits = null;
    $pushup = false;
    if ($gameMin !== null) {
        foreach ($lengthMatrix as $length) {
            if (($length['from'] < $gameMin && $length['till'] >= $gameMin) || $pushup) {
                if (($length['till'] + $length['threshold']) >= $gameMax) {
                    $gameFits = $length['id'];
                } else {
                    $pushup = true;
                }
            }
        }

        if ($gameFits) {
            $database->insert('game2tag', ['game_id' => $gameId, 'tag_id' => $gameFits]);
        }
    }

    // determine game weight tag

    if (isset($gameData['averageweight']) && $gameData['averageweight'] !== null) {
        foreach ($complexityMatrix as $compexity) {
            if ($compexity['from'] <= $gameData['averageweight'] && $length['till'] > $gameData['averageweight']) {
                $gameFits = $compexity['id'];
            }
        }

        if ($gameFits) {
            $database->insert('game2tag', ['game_id' => $gameId, 'tag_id' => $gameFits]);
        }
    }

}
