{"name": "explain-games-party-api", "version": "0.0.1", "description": "API for explain games party", "main": "./root/index.js", "engines": {"node": ">=22.11"}, "scripts": {"dev": "npx nodemon root/index.ts", "start": "npx ts-node root/index.ts", "lint:prettier": "npx prettier ./root/** --write", "lint:es": "npx eslint root/**", "lint:fix": "npx eslint root/** --fix", "lint": "npm run lint:prettier && npm run lint:es"}, "keywords": ["boardgames", "manager"], "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.699.0", "@trpc/server": "11.0.0-rc.608", "@tsconfig/node-lts": "22.0.0", "@types/cookie-parser": "^1.4.3", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/multer": "^1.4.12", "body-parser": "^1.18.3", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^16.4.5", "drizzle-kit": "^0.28.0", "drizzle-orm": "^0.36.1", "drizzle-zod": "^0.5.1", "express": "^5.0.1", "express-oauth2-jwt-bearer": "^1.6.0", "firebase-admin": "^13.4.0", "multer": "1.4.5-lts.1", "mysql2": "^3.11.4", "nodemon": "^3.1.7", "react-hook-form": "^7.53.2", "sharp": "^0.33.5", "ts-node": "^10.9.2", "typescript": "^5.7.2", "uniqolor": "^1.1.1", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.14.0", "eslint": "^9.14.0", "eslint-plugin-import": "^2.31.0", "globals": "^15.12.0", "prettier": "^3.3.3", "typescript-eslint": "^8.13.0"}}