import * as fs from "node:fs"
import * as path from "node:path"

import { TRPCError } from "@trpc/server"
import { and, count, eq } from "drizzle-orm"

import { db } from "../db"
import { communitySchema } from "../db/schema/community.schema"
import { permissionUserToRoleSchema } from "../db/schema/permissionUserToRole.schema"
import { userToCommunitySchema } from "../db/schema/userToCommunity.schema"
import { hasPermission } from "../permissions"
import { protectedProcedure } from "../queries/trpc"
import { createCommunityInputs } from "../schemas"
import { defaultCommunityPicture } from "../upload/defaultCommunityPicture"

export const createCommunity = protectedProcedure
  .input(createCommunityInputs)
  .mutation(async ({ input, ctx: { loginData } }) => {
    const countCommunities = await db
      .select({ count: count() })
      .from(permissionUserToRoleSchema)
      .where(
        and(
          eq(permissionUserToRoleSchema.userId, loginData.id),
          eq(permissionUserToRoleSchema.roleId, "owner"),
          eq(permissionUserToRoleSchema.subject, "community"),
        ),
      )
      .then((count) => count[0].count)

    if (
      !hasPermission(loginData, "global", "createCommunity", {
        createdCommunities: countCommunities,
      })
    ) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "You can't create new community",
      })
    }

    const insertResult = await db
      .insert(communitySchema)
      .values({
        name: input.name,
        location: input.location ?? null,
        online: input.online ?? null,
        openness: input.openness,
        memberApproval: input.approval ?? true,
        allowShare: input.allowShare ?? true,
        description: input.description ?? null,
        image: "",
      })
      .$returningId()
      .then((re) => re[0])

    const communityId = insertResult.id

    db.insert(userToCommunitySchema)
      .values({
        userId: loginData.id,
        communityId: communityId,
        trust: 0,
        shareMyGames: false,
      })
      .then((re) => re)

    db.insert(permissionUserToRoleSchema)
      .values({
        userId: loginData.id,
        roleId: "member",
        subject: "community",
        subjectId: communityId,
      })
      .then((re) => re)

    db.insert(permissionUserToRoleSchema)
      .values({
        userId: loginData.id,
        roleId: "owner",
        subject: "community",
        subjectId: communityId,
      })
      .then((re) => re)

    const buffer = fs.readFileSync(
      path.resolve(__dirname, "../assets/default_community_logo.png"),
    )

    defaultCommunityPicture(buffer, communityId)

    return { success: true, communityId }
  })
