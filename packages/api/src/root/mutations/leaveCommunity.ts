import { TRPCError } from "@trpc/server"
import { and, eq } from "drizzle-orm"
import { z } from "zod"

import { db } from "../db"
import { permissionUserToRoleSchema } from "../db/schema/permissionUserToRole.schema"
import { userToCommunitySchema } from "../db/schema/userToCommunity.schema"
import { protectedProcedure } from "../queries/trpc"

export const leaveCommunity = protectedProcedure
  .input(
    z.object({
      communityId: z.number(),
    }),
  )
  .mutation(async ({ input, ctx: { loginData } }) => {
    const connectionCheck = await db
      .select({ communityId: userToCommunitySchema.communityId })
      .from(userToCommunitySchema)
      .where(
        and(
          eq(userToCommunitySchema.userId, loginData.id),
          eq(userToCommunitySchema.communityId, input.communityId),
        ),
      )
      .then((communityId) => communityId[0])

    if (!connectionCheck) {
      throw new TRPCError({
        code: "PRECONDITION_FAILED",
        message: "No connection to this community",
      })
    }

    const re = await db
      .delete(userToCommunitySchema)
      .where(
        and(
          eq(userToCommunitySchema.userId, loginData.id),
          eq(userToCommunitySchema.communityId, input.communityId),
        ),
      )
      .then(() => "OK")

    await db
      .delete(permissionUserToRoleSchema)
      .where(
        and(
          eq(permissionUserToRoleSchema.userId, loginData.id),
          eq(permissionUserToRoleSchema.subject, "community"),
          eq(permissionUserToRoleSchema.subjectId, input.communityId),
        ),
      )
      .then(() => "OK")

    return "OK"
  })
