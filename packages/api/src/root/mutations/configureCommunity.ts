import { TRPCError } from "@trpc/server"
import { eq } from "drizzle-orm"

import { db } from "../db"
import { communitySchema } from "../db/schema/community.schema"
import { hasPermission } from "../permissions"
import { protectedProcedure } from "../queries/trpc"
import { createCommunityInputs } from "../schemas"

export const configureCommunity = protectedProcedure
  .input(createCommunityInputs)
  .mutation(async ({ input, ctx: { loginData } }) => {
    if (!input.id) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "No community ID",
      })
    }

    const communityCheck = await db
      .select({ id: communitySchema.id })
      .from(communitySchema)
      .where(eq(communitySchema.id, input.id!))
      .then((community) => community[0])

    if (!communityCheck) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "No community",
      })
    }

    if (!hasPermission(loginData, "community", "update", { id: input.id! })) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "You can't create new community",
      })
    }

    await db
      .update(communitySchema)
      .set({
        name: input.name,
        location: input.location ?? null,
        online: input.online ?? null,
        openness: input.openness,
        memberApproval: input.approval ?? true,
        allowShare: input.allowShare ?? true,
        description: input.description ?? null,
        welcome: input.welcome ?? null,
      })
      .where(eq(communitySchema.id, input.id!))
      .then((re) => re[0])

    return { success: true, communityId: input.id }
  })
