import { int, mysqlEnum, mysqlTable, varchar } from "drizzle-orm/mysql-core"

import { usersSchema } from "./users.schema"

export const permissionUserToRoleSchema = mysqlTable("permission_user2role", {
  userId: int("user_id")
    .notNull()
    .references(() => usersSchema.id),
  subjectId: int("subject_id"),
  roleId: varchar("role_id", { length: 20 }).notNull(),
  id: int().notNull().autoincrement(),
  subject: mysqlEnum(["global", "community", "userdata"]).notNull(),
})
