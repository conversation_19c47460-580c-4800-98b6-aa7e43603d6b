import {
  boolean,
  mysqlEnum,
  mysqlTable,
  text,
  varchar,
} from "drizzle-orm/mysql-core"

import { commonCreated, commonId } from "./common"

export const communitySchema = mysqlTable("community", {
  ...commonId,
  name: varchar({ length: 256 }).notNull(),
  image: varchar({ length: 256 }).notNull(),
  openness: mysqlEnum([
    "public",
    "private",
    "publicLimited",
    "closed",
  ]).notNull(),
  memberApproval: boolean("member_approval").notNull(),
  allowShare: boolean("allow_share").notNull(),
  location: varchar("location", { length: 256 }),
  online: varchar("online", { length: 256 }),
  description: text("description"),
  welcome: text("welcome"),
  ...commonCreated,
})
