import { changeUserStatus } from "./mutations/changeUserStatus"
import { configureCommunity } from "./mutations/configureCommunity"
import { createCommunity } from "./mutations/createCommunity"
import { createInvite } from "./mutations/createInvite"
import { joinByInvite } from "./mutations/joinByInvite"
import { joinCommunity } from "./mutations/joinCommunity"
import { leaveCommunity } from "./mutations/leaveCommunity"
import { switchGameShare } from "./mutations/switchGameShare"
import { userProfileUpdate } from "./mutations/userProfileUpdate"
import { communityBasic } from "./queries/communityBasic"
import { communityExpanded2 } from "./queries/communityExpanded2"
import { communityGameExtended2 } from "./queries/communityGameExtended2"
import { communityGamesList2 } from "./queries/communityGamesList2"
import { communityList } from "./queries/communityList"
import { communityUserInfo } from "./queries/communityUserInfo"
import { communityUserList } from "./queries/communityUserList"
import { getMyGameInfo } from "./queries/getMyGameInfo"
import { getMyInfo } from "./queries/getMyInfo"
import { joinByInvitePrejoin } from "./queries/joinByInvitePrejoin"
import { loggedInUser } from "./queries/loggedInUser"
import { publicCommunityList } from "./queries/publicCommunityList"
import { tagList } from "./queries/tagList"
import { router } from "./queries/trpc"

export const appRouter = router({
  communityList,
  loggedInUser,
  communityExpanded2,
  communityGamesList2,
  communityBasic,
  communityGameExtended2,
  communityUserList,
  communityUserInfo,
  leaveCommunity,
  userProfileUpdate,
  getMyInfo,
  publicCommunityList,
  joinCommunity,
  joinByInvite,
  changeUserStatus,
  createCommunity,
  configureCommunity,
  createInvite,
  switchGameShare,
  getMyGameInfo,
  tagList,
  joinByInvitePrejoin,
  /*
    userCreate: publicProcedure
      .input(z.object({ name: z.string() }))
      .mutation(async (opts) => {
        const { input } = opts

        const input: {
          name: string
        }
        // Create a new user in the database
        const user = await db.user.create(input)

        const user: {
          name: string
          id: string
        }
        return user
      }),
    userById: publicProcedure.input(z.string()).query(async (opts) => {
      const { input } = opts

      const input: string
      // Retrieve the user with the given ID
      const user = await db.user.findById(input)

      const user: User | undefined
      return user
    }),
       */
}) // ...

export type AppRouter = typeof appRouter
