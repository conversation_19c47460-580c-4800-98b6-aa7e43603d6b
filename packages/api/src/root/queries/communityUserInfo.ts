import { TRPCError } from "@trpc/server"
import { and, desc, eq } from "drizzle-orm"
import { z } from "zod"

import { db } from "../db"
import { getUserGames } from "../db/queries/getUserGames"
import { tagsAndCats } from "../db/queries/tagsAndCats"
import { permissionUserToRoleSchema } from "../db/schema/permissionUserToRole.schema"
import { userToCommunitySchema } from "../db/schema/userToCommunity.schema"
import { usersSchema } from "../db/schema/users.schema"
import { hasCommunityRole, hasPermission } from "../permissions"

import { communityProcedure } from "./trpc"

export const communityUserInfo = communityProcedure
  .input(z.object({ communityId: z.number(), userId: z.number() }))
  .query(async ({ input, ctx: { loginData } }) => {
    if (
      !hasPermission(loginData, "community", "communityUserData", {
        id: input.communityId,
      })
    ) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Unauthorized: : You cannot see this data",
      })
    }

    const communityUser = db
      .select({
        id: usersSchema.id,
        name: usersSchema.name,
        bggUsername: usersSchema.bggUsername,
        color: usersSchema.color,
        avatar: usersSchema.avatar,
        shareMyGames: userToCommunitySchema.shareMyGames,
        gameCount: usersSchema.gameCount,
      })
      .from(usersSchema)
      .innerJoin(
        userToCommunitySchema,
        eq(usersSchema.id, userToCommunitySchema.userId),
      )
      .where(
        and(
          eq(userToCommunitySchema.communityId, input.communityId),
          eq(userToCommunitySchema.userId, input.userId),
        ),
      )
      .orderBy(desc(userToCommunitySchema.created))
      .then(async (userResponse) => {
        const user = userResponse[0]
        let games
        let tags

        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          })
        }

        if (user.shareMyGames) {
          games = await getUserGames(user.id)
          tags = await tagsAndCats()
        }

        const bggUsername = user.shareMyGames ? user.bggUsername : null

        const roles = await db
          .select({
            role: permissionUserToRoleSchema.roleId,
            subject: permissionUserToRoleSchema.subject,
            subjectId: permissionUserToRoleSchema.subjectId,
          })
          .from(permissionUserToRoleSchema)
          .where(
            and(
              eq(permissionUserToRoleSchema.userId, user.id),
              eq(permissionUserToRoleSchema.subject, "community"),
              eq(permissionUserToRoleSchema.subjectId, input.communityId),
            ),
          )
          .then((roles) => roles)

        if (roles.length === 0) {
          throw new TRPCError({
            code: "UNAUTHORIZED",
            message: "Unauthorized: : You cannot see this data",
          })
        }

        if (
          hasCommunityRole(roles, "invited", input.communityId) &&
          !hasPermission(loginData, "community", "approve", {
            id: input.communityId,
          })
        ) {
          throw new TRPCError({
            code: "UNAUTHORIZED",
            message: "Unauthorized: : You cannot see this data",
          })
        }

        return { ...user, bggUsername, games, roles, ...tags }
      })

    return communityUser
  })
