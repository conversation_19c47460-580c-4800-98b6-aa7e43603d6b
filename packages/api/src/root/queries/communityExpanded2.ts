import { and, desc, eq, inArray } from "drizzle-orm"
import { z } from "zod"

import { db } from "../db"
import { gameToCommunity2Schema } from "../db/schema/gameToCommunity2.schema"
import { gamesSchema } from "../db/schema/games.schema"
import { userToCommunitySchema } from "../db/schema/userToCommunity.schema"
import { usersSchema } from "../db/schema/users.schema"
import { BggGameDataExtracted, GameUsersShort } from "../types/game.types"

import { communityProcedure } from "./trpc"

const COMMUNITY_HOME_GAME_COUNT = 20
const COMMUNITY_HOME_USER_COUNT = 20

export const communityExpanded2 = communityProcedure
  .input(z.object({ communityId: z.number() }))
  .query(async ({ input }) => {
    const newestGames = await db
      .select({
        news: gameToCommunity2Schema.news,
        userCount: gameToCommunity2Schema.userCount,
        id: gamesSchema.id,
        title: gamesSchema.title,
        bggId: gamesSchema.bggId,
        users: gameToCommunity2Schema.users,
        bggInfo: gamesSchema.bggInfo,
      })
      .from(gameToCommunity2Schema)
      .innerJoin(gamesSchema, eq(gameToCommunity2Schema.gameId, gamesSchema.id))
      .where(
        and(
          eq(gameToCommunity2Schema.communityId, input.communityId),
          eq(gamesSchema.type, "base"),
        ),
      )
      .orderBy(desc(gameToCommunity2Schema.news))
      .limit(COMMUNITY_HOME_GAME_COUNT)
      .then(async (games) => {
        let uniqueUsers: number[] = []
        const gamesUpdated = games.map((game) => {
          const users: GameUsersShort[] = JSON.parse(
            game.users ?? "[]",
          ) as GameUsersShort[]

          const bggInfoData: BggGameDataExtracted = JSON.parse(
            game.bggInfo ?? "{}",
          ) as BggGameDataExtracted

          uniqueUsers = uniqueUsers.concat(
            users
              .map((user) => user[0])
              .filter((user) => uniqueUsers.indexOf(user) === -1),
          )

          return {
            ...game,
            users,
            average: Math.round(parseFloat(bggInfoData.average) * 10) / 10,
          }
        })

        const users = await db
          .select({
            name: usersSchema.name,
            id: usersSchema.id,
            avatar: usersSchema.avatar,
            color: usersSchema.color,
          })
          .from(usersSchema)
          .where(inArray(usersSchema.id, uniqueUsers))

        return { games: gamesUpdated, users }
      })

    const communityUsers = await db
      .select({
        id: usersSchema.id,
        name: usersSchema.name,
        email: usersSchema.email,
        bggUserName: usersSchema.bggUsername,
        color: usersSchema.color,
        gameCount: usersSchema.gameCount,
        avatar: usersSchema.avatar,
      })
      .from(usersSchema)
      .innerJoin(
        userToCommunitySchema,
        eq(usersSchema.id, userToCommunitySchema.userId),
      )
      .where(eq(userToCommunitySchema.communityId, input.communityId))
      .orderBy(desc(userToCommunitySchema.created))
      .limit(COMMUNITY_HOME_USER_COUNT)
      .then((users) => {
        return users
      })

    return {
      games: newestGames,
      users: communityUsers,
    }
  })
