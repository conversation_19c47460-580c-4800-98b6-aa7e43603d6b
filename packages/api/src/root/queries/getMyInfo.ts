import { eq } from "drizzle-orm"

import { db } from "../db"
import { getUserGames } from "../db/queries/getUserGames"
import { tagsAndCats } from "../db/queries/tagsAndCats"
import { communitySchema } from "../db/schema/community.schema"
import { userToCommunitySchema } from "../db/schema/userToCommunity.schema"
import { usersSchema } from "../db/schema/users.schema"

import { protectedProcedure } from "./trpc"

export const getMyInfo = protectedProcedure.query(
  async ({ ctx: { loginData } }) => {
    const profile = await db
      .select({
        id: usersSchema.id,
        name: usersSchema.name,
        created: usersSchema.created,
        active: usersSchema.active,
        bggUsername: usersSchema.bggUsername,
        color: usersSchema.color,
        email: usersSchema.email,
        avatar: usersSchema.avatar,
        gameCount: usersSchema.gameCount,
      })
      .from(usersSchema)
      .where(eq(usersSchema.id, loginData.id))
      .then((user) => user[0])

    const communities = await db
      .select({
        id: userToCommunitySchema.communityId,
        shareMyGames: userToCommunitySchema.shareMyGames,
        name: communitySchema.name,
        openness: communitySchema.openness,
        memberApproval: communitySchema.memberApproval,
        allowShare: communitySchema.allowShare,
      })
      .from(userToCommunitySchema)
      .innerJoin(
        communitySchema,
        eq(userToCommunitySchema.communityId, communitySchema.id),
      )
      .where(eq(userToCommunitySchema.userId, loginData.id))
      .then((communities) => communities)

    const games = await getUserGames(loginData.id)

    const tags = await tagsAndCats()

    return { profile, communities, games, ...tags }
  },
)
