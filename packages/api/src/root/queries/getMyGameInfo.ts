import { and, eq } from "drizzle-orm"
import { z } from "zod"

import { db } from "../db"
import { gameToExpansionSchema } from "../db/schema/gameToExpansion.schema"
import { gameToTagSchema } from "../db/schema/gameToTag.schema"
import { gamesSchema } from "../db/schema/games.schema"
import { tagCategoriesSchema } from "../db/schema/tagCategories.schema"
import { tagsSchema } from "../db/schema/tags.schema"
import { userToBaseGameSchema } from "../db/schema/userToBaseGame.schema"
import { userToGameSchema } from "../db/schema/userToGame.schema"
import { BggGameDataExtracted } from "../types/game.types"

import { protectedProcedure } from "./trpc"

export const getMyGameInfo = protectedProcedure
  .input(z.object({ gameId: z.number() }))
  .query(async ({ input, ctx: { loginData } }) => {
    const myData = await db
      .select({
        lastUpdated: userToBaseGameSchema.lastUpdated,
        lastPlay: userToBaseGameSchema.lastPlay,
        rating: userToBaseGameSchema.rating,
        portability: userToBaseGameSchema.portability,
        events: userToBaseGameSchema.events,
        willTeach: userToBaseGameSchema.willTeach,
        playCount: userToBaseGameSchema.playCount,
        playPriority: userToBaseGameSchema.playPriority,
      })
      .from(userToBaseGameSchema)
      .where(
        and(
          eq(userToBaseGameSchema.gameId, input.gameId),
          eq(userToBaseGameSchema.userId, loginData.id),
        ),
      )
      .then((data) => data[0])

    const game = await db
      .select({
        id: gamesSchema.id,
        title: gamesSchema.title,
        bggId: gamesSchema.bggId,
        bggInfo: gamesSchema.bggInfo,
      })
      .from(gamesSchema)
      .where(eq(gamesSchema.id, input.gameId))
      .then(async (loadGame) => {
        const game = loadGame[0]
        const bggDataExtracted: BggGameDataExtracted = JSON.parse(
          game.bggInfo ?? "{}",
        ) as BggGameDataExtracted

        return {
          id: game.id,
          title: game.title,
          bggId: game.bggId,
          players: {
            box: {
              min: parseInt(bggDataExtracted.players.box.min ?? "0"),
              max: parseInt(bggDataExtracted.players.box.max ?? "0"),
            },
            stats: bggDataExtracted.players.stats?.map((stat) => [
              parseInt(stat.players),
              stat.status,
              String(parseInt(stat.players)) != stat.players,
            ]),
          },
          length: {
            box: {
              min: parseInt(bggDataExtracted.playTime.box.min ?? "0"),
              max: parseInt(bggDataExtracted.playTime.box.max ?? "0"),
            },
          },
          age: bggDataExtracted.age,
          average: Math.round(parseFloat(bggDataExtracted.average) * 100) / 100,
        }
      })

    const expansions = await db
      .select({
        id: gamesSchema.id,
        title: gamesSchema.title,
        bggId: gamesSchema.bggId,
        bggInfo: gamesSchema.bggInfo,
        type: gamesSchema.type,
      })
      .from(gameToExpansionSchema)
      .innerJoin(
        gamesSchema,
        eq(gameToExpansionSchema.expansionId, gamesSchema.id),
      )
      .innerJoin(
        userToGameSchema,
        and(
          eq(userToGameSchema.gameId, gamesSchema.id),
          eq(userToGameSchema.userId, loginData.id),
        ),
      )
      .where(eq(gameToExpansionSchema.gameId, input.gameId))
      .then((games) => {
        return games.map((game) => {
          const bggDataExtracted: BggGameDataExtracted = JSON.parse(
            game.bggInfo ?? "{}",
          ) as BggGameDataExtracted

          return {
            type: game.type,
            id: game.id,
            title: game.title,
            bggId: game.bggId,
            average:
              Math.round(parseFloat(bggDataExtracted.average) * 100) / 100,
          }
        })
      })

    const tags = await db
      .select({
        id: tagsSchema.id,
        type: tagCategoriesSchema.title,
        typeId: tagsSchema.type,
        title: tagsSchema.title,
        color: tagCategoriesSchema.color,
      })
      .from(gameToTagSchema)
      .innerJoin(tagsSchema, eq(gameToTagSchema.tagId, tagsSchema.id))
      .innerJoin(
        tagCategoriesSchema,
        eq(tagsSchema.type, tagCategoriesSchema.id),
      )
      .where(eq(gameToTagSchema.gameId, input.gameId))
      .then((tags) => {
        return tags
      })

    return {
      tags,
      game,
      expansions,
      myData,
    }
  })
