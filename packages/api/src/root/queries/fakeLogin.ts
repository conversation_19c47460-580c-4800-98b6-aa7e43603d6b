import { eq } from "drizzle-orm"

import { db } from "../db"
import { permissionUserToRoleSchema } from "../db/schema/permissionUserToRole.schema"
import { roleSettingsSchema } from "../db/schema/roleSettings.schema"

export const fakeLogin = async () => {
  return {
    ctx: {
      // Infers the `session` as non-nullable
      auth: {
        payload: {
          sub: "google-oauth2|110962012279882593616",
        },
      },
      loginData: {
        id: 1,
        roles: await db
          .select({
            role: permissionUserToRoleSchema.roleId,
            subject: permissionUserToRoleSchema.subject,
            subjectId: permissionUserToRoleSchema.subjectId,
          })
          .from(permissionUserToRoleSchema)
          .where(eq(permissionUserToRoleSchema.userId, 1))
          .then(async (roles) => {
            return await Promise.all(
              roles.map(async (role) => {
                const roleSettings = await db
                  .select({
                    name: roleSettingsSchema.name,
                    value: roleSettingsSchema.value,
                  })
                  .from(roleSettingsSchema)
                  .where(eq(roleSettingsSchema.roleId, role.role))
                  .then((settings) => settings)

                return { ...role, roleSettings }
              }),
            )
          }),
      },
    },
  }
}
