import { and, desc, eq } from "drizzle-orm"
import { z } from "zod"

import { db } from "../db"
import { permissionUserToRoleSchema } from "../db/schema/permissionUserToRole.schema"
import { userToCommunitySchema } from "../db/schema/userToCommunity.schema"
import { usersSchema } from "../db/schema/users.schema"
import { hasCommunityRole, hasPermission } from "../permissions"

import { communityProcedure } from "./trpc"

export const communityUserList = communityProcedure
  .input(z.object({ communityId: z.number() }))
  .query(async ({ input, ctx: { loginData } }) => {
    const communityUsers = await db
      .select({
        id: usersSchema.id,
        name: usersSchema.name,
        color: usersSchema.color,
        avatar: usersSchema.avatar,
        gameCount: usersSchema.gameCount,
      })
      .from(usersSchema)
      .innerJoin(
        userToCommunitySchema,
        eq(usersSchema.id, userToCommunitySchema.userId),
      )
      .where(eq(userToCommunitySchema.communityId, input.communityId))
      .orderBy(desc(userToCommunitySchema.created))
      .then(async (users) => {
        const usersWithRoles = []

        for (const user of users) {
          const userRoles = await db
            .select({
              role: permissionUserToRoleSchema.roleId,
              subject: permissionUserToRoleSchema.subject,
              subjectId: permissionUserToRoleSchema.subjectId,
            })
            .from(permissionUserToRoleSchema)
            .where(
              and(
                eq(permissionUserToRoleSchema.userId, user.id),
                eq(permissionUserToRoleSchema.subject, "community"),
                eq(permissionUserToRoleSchema.subjectId, input.communityId),
              ),
            )
            .then((roles) => roles)

          usersWithRoles.push({
            ...user,
            roles: userRoles,
          })
        }

        return usersWithRoles
      })

    const users = hasPermission(loginData, "community", "approve", {
      id: input.communityId,
    })
      ? communityUsers
      : communityUsers.filter(
          (user) =>
            !hasCommunityRole(user.roles, ["invited"], input.communityId),
        )

    return users
  })
