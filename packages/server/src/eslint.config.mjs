import { fixupPluginRules } from "@eslint/compat"
import pluginJs from "@eslint/js"
import importPlugin from "eslint-plugin-import"
import eslintPluginReactHooks from "eslint-plugin-react-hooks"
import reactHooksAddons from "eslint-plugin-react-hooks-addons"
import reactHooksExtra from "eslint-plugin-react-hooks-extra"
import globals from "globals"
import tseslint from "typescript-eslint"

/** @type {import('eslint').Linter.Config[]} */
export default [
  { files: ["**/*.{ts}", "**/*.{svg}"] },
  { languageOptions: { globals: globals.browser } },
  pluginJs.configs.recommended,
  importPlugin.flatConfigs.recommended,
  {
    plugins: {
      "react-hooks": fixupPluginRules(eslintPluginReactHooks),
      "react-hooks-extra": reactHooksExtra,
      "react-hooks-addons": reactHooksAddons,
    },
    rules: {
      "import/no-unresolved": "off",
      "import/order": [
        "error",
        {
          alphabetize: {
            order: "asc",
          },
          named: true,
          "newlines-between": "always",
          groups: [
            "builtin",
            "external",
            "parent",
            "sibling",
            "index",
            "object",
            "type",
          ],
        },
      ],
      "import/exports-last": "off",
      "react-hooks-addons/no-unused-deps": "warn",
    },
  },
  ...tseslint.configs.recommended,
]
