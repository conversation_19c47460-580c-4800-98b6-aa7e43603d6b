{"compilerOptions": {"outDir": "build", "module": "esnext", "target": "es5", "lib": ["es6", "dom", "dom.iterable", "esnext"], "sourceMap": true, "allowJs": false, "jsx": "react-jsx", "strict": true, "moduleResolution": "node", "rootDirs": ["./src", "../router", "../common"], "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": true, "strictNullChecks": true, "isolatedModules": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "downlevelIteration": true}, "exclude": ["node_modules", "dist"], "types": ["typePatches"]}