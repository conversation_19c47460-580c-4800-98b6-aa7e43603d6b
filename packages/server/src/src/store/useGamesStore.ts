import { create } from "zustand/index"

import {
  ICommunityGame,
  IGame<PERSON>ser,
  ITag,
  ITagCategory,
} from "../types/tRPC.types"

export type OrderByType = keyof ICommunityGame | "search"

interface GameMeta {
  order: "asc" | "desc"
  orderBy: OrderByType
  search?: string
  minPlayers?: number
  maxPlayers?: number
  playerLevel?: number
  communityId: number
}

export type GameStoreCollection = {
  meta: GameMeta
  games: ICommunityGame[]
  users: IGameUser[]
  tags: ITag[]
  populatedUsers?: (IGameUser | undefined)[]
  populatedTags?: (ITag | undefined)[][]
  accessId?: number
  tagCategories: ITagCategory[]
}

export interface UseGameStoreProps {
  games: GameStoreCollection[]
  setGames: (newGames: GameStoreCollection) => void
  getGames: (communityId?: number) => GameStoreCollection | undefined
  getPopulatedUser: (
    userId: number,
    communityId?: number,
  ) => IGameUser | undefined
  setMeta: (meta: GameMeta) => void
  accessId: number
  revitalizeCollection: (communityId?: number) => void
  lastAccessedCommunityId: number
}

export function isOrderBy(key: string | OrderByType): key is OrderByType {
  return (
    key === "title" ||
    key === "id" ||
    key === "news" ||
    key == "average" ||
    key == "search"
  )
}

export function isOrderByGame(
  key: string | keyof ICommunityGame,
): key is keyof ICommunityGame {
  return isOrderBy(key) && key !== "search"
}

const MAX_STORED_COLLECTIONS = 5

export const useGameStore = create<UseGameStoreProps>((set, get) => ({
  games: [],
  accessId: 0,
  lastAccessedCommunityId: 0,
  setMeta: (meta: GameMeta) =>
    set((state) => {
      const collection = getCollection(state.games, meta.communityId)
      let isDiff = false

      if (!collection) {
        return {}
      }

      Object.keys(meta).forEach((key) => {
        if (
          meta[key as keyof typeof meta] !==
          collection.meta[key as keyof typeof collection.meta]
        ) {
          isDiff = true
        }
      })

      if (!isDiff) {
        return {}
      }

      const updatedCollection = {
        ...collection,
        lastAccessedCommunityId: meta.communityId,
        meta: { ...collection.meta, ...meta },
      }

      return updateAndWrite(state, updatedCollection)
    }),
  setGames: (newGames: GameStoreCollection) =>
    set((state) => {
      return updateAndWrite(state, newGames, true)
    }),
  getGames: (communityId?: number): GameStoreCollection | undefined => {
    const useCommunityId = communityId ?? get().lastAccessedCommunityId
    return getCollection(get().games, useCommunityId)
  },
  getPopulatedUser: (userId: number, communityId?: number) => {
    const useCommunityId = communityId ?? get().lastAccessedCommunityId
    return getCollection(get().games, useCommunityId)?.populatedUsers?.[userId]
  },
  revitalizeCollection: (communityId?: number) =>
    set((state) => {
      const revitalizeGame = getCollection(
        state.games,
        communityId ?? state.lastAccessedCommunityId,
      )

      if (!revitalizeGame) {
        return {}
      }

      return updateAndWrite(state, revitalizeGame)
    }),
}))

function getCollection(
  games: GameStoreCollection[],
  communityId: number,
): GameStoreCollection | undefined {
  return games.find((game) => {
    return game.meta.communityId === communityId
  })
}

function updateAndWrite(
  state: UseGameStoreProps,
  newGame: GameStoreCollection,
  populateEntries: boolean = false,
): Partial<UseGameStoreProps> {
  // remove old game collections from state
  const cleanedGames = state.games.filter((game) => {
    return (
      newGame.meta.communityId !== game.meta.communityId &&
      (game.accessId ?? 0) > state.accessId - MAX_STORED_COLLECTIONS
    )
  })

  const nextAccessId = state.accessId + 1

  const populatedTags: ITag[][] = []
  const populatedUsers: IGameUser[] = []

  if (populateEntries) {
    newGame.games.forEach((currentGame) => {
      populatedTags[currentGame.id] = currentGame.tags!.map(
        (currentTag) => newGame.tags.find((tag) => tag.id === currentTag)!,
      )
    })

    newGame.users.forEach((user) => {
      populatedUsers[user.id] = user
    })
  }

  return {
    games: [
      ...cleanedGames,
      {
        tags: newGame.tags,
        users: newGame.users,
        tagCategories: newGame.tagCategories,
        populatedUsers: populateEntries
          ? populatedUsers
          : newGame.populatedUsers,
        populatedTags: populateEntries ? populatedTags : newGame.populatedTags,
        games: newGame.games,
        meta: { ...newGame.meta },
        accessId: nextAccessId,
      },
    ],
    lastAccessedCommunityId: newGame.meta.communityId,
    accessId: nextAccessId,
  }
}
