import { create } from "zustand"

import { BreadcrumbLink } from "../types/global"

interface UseBreadcrumbsStoreProps {
  breadcrumbs: BreadcrumbLink[]
  setBreadcrumbs: (breadcrumbs: BreadcrumbLink[]) => void
}

export const useBreadcrumbsStore = create<UseBreadcrumbsStoreProps>((set) => ({
  breadcrumbs: [],
  setBreadcrumbs: (breadcrumbs: BreadcrumbLink[]) =>
    set(() => ({ breadcrumbs })),
}))
