import { create } from "zustand/index"

import { RoleData } from "../permissions"

interface MyData {
  email?: string | null
  roles?: RoleData[]
  trust?: number
  name?: string | null
  bggUsername?: string | null
  avatar?: string | null
  color?: string | null
  id?: number
  isNew?: boolean
  countCommunities?: number
}

export interface UseUserStoreProps {
  authToken: string | null
  userData: MyData
  userLoaded: boolean
  setAuthToken: (userAuthToken: string) => void
  setUserData: (userData: MyData) => void
  setUserLoaded: () => void
  isLoggedIn: boolean
}

export const useUserStore = create<UseUserStoreProps>((set) => {
  return {
    userData: {},
    isLoggedIn: false,
    authToken: null,
    userLoaded: false,
    setUserLoaded: () => set(() => ({ userLoaded: true })),
    setUserData: (userData: MyData) =>
      set(() => ({
        userData,
        userLoaded: true,
        isLoggedIn: userData && userData.id !== undefined,
      })),
    setAuthToken: (userAuthToken: string) =>
      set(() => ({ authToken: userAuthToken })),
  }
})
