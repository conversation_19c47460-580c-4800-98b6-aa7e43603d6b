import { Box } from "@mui/material"
import { getRouteApi } from "@tanstack/react-router"
import classnames from "classnames"
import React from "react"

import { Breadcrumbs } from "../../components/Breadcurmbs/Breadcrumbs"
import { FillData } from "../../components/FillData/FillData"
import { PartyBox } from "../../components/elements/link/PartyBox/PartyBox"
import { COMMUNITY_IMAGES } from "../../config/images"
import { COMMUNITIES_ROOT_ROUTE, COMMUNITY_ROUTE } from "../../routes/paths"
import { useIsMobileStore } from "../../store/useIsMobileStore"

import * as styles from "./layout.css"

const route = getRouteApi(COMMUNITIES_ROOT_ROUTE)
export const LayoutCommunity = ({
  children,
}: {
  children: React.ReactNode
}) => {
  const data = route.useLoaderData()
  const params = route.useParams()
  const sizeThresholdList = useIsMobileStore((state) => state.sizeThresholdList)

  return (
    <Box className={styles.container}>
      <FillData />
      {!sizeThresholdList.largeTablet && <Breadcrumbs />}
      <PartyBox
        to={COMMUNITY_ROUTE}
        params={params}
        title={data?.name ?? ""}
        className={classnames(styles.logo, {
          [styles.nonMobile]: !sizeThresholdList.largeTablet,
        })}
        sx={{
          backgroundImage: `url(${ENV_IMAGE_CDN}${COMMUNITY_IMAGES}/small_${data?.image ?? ""})`,
        }}
      />
      <div className={styles.layout}>{children}</div>
    </Box>
  )
}
