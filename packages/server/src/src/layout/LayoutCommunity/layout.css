@value isTablet from "../../css/size.css";

html,
body {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  font-family: "Roboto", "Helvetica", "Arial", sans-serif;
}

.base {
  margin: 0;
  font-family: "Roboto", "Helvetica", "Arial", sans-serif;
  font-weight: 500;
  font-size: 1.25rem;
  line-height: 1.6;
  letter-spacing: 0.0075em;
  color: rgba(0, 0, 0, 0.6);
}

h1 {
  margin: 0;
  font-weight: bold;
  font-size: 2.4292rem;
}

h2 {
  margin: 0;
  font-weight: 500;
  font-size: 2.125rem;
  letter-spacing: 0.00735em;
}

h3 {
  margin: 0;
  font-weight: 500;
  font-size: 1.5rem;
  letter-spacing: 0em;
}

h4 {
  margin: 0;
  font-weight: 400;
  font-size: 1rem;
  letter-spacing: 0.00938em;
}

h5 {
  margin: 0;
  font-weight: 400;
  font-size: 1rem;
  letter-spacing: 0.00938em;
}

h6 {
  margin: 0;
  font-weight: 400;
  font-size: 1rem;
  letter-spacing: 0.00938em;
}


:root {
  --spacing-q: 2px;
  --spacing-h: 4px;
  --spacing-1: 8px;
  --spacing-2: 16px;
  --spacing-3: 24px;
  --spacing-4: 32px;
  --spacing-5: 40px;
  --spacing-6: 48px;
  --spacing-7: 56px;
  --spacing-8: 64px;
  --spacing-9: 72px;
  --spacing-10: 80px;
  --spacing-11: 88px;
}


.layout {
  height: 100%;
  max-width: 1536px;
  margin: auto;
}

.container {
  max-width: 1536px;
  margin: auto;
  position: relative;
  padding: 0 var(--spacing-2);
}

.logo {
  cursor: pointer;
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 2000;
  border-radius: 10px;
  overflow: hidden;
  width: 100px;
  height: 100px;
  background-size: cover;
}

.nonMobile {
  top: -48px;
}

@media (max-width: isTablet) {
  .logo {
    position: fixed;
  }
}

.logoImg {
  width: 100px;
}