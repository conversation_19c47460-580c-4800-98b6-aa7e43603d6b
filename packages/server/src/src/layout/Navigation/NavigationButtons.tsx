import { Box } from "@mui/material"
import React from "react"

import { LoginButton } from "../../components/LoginButton/LoginButton"
import { PartyLink } from "../../components/elements/link/PartyLink/PartyLink"
import {
  COMMUNITY_OPEN_ROUTE,
  INDEX_ROUTE,
  PROFILE_ROUTE,
  PUBLIC_COMMUNITIES_ROUTE,
  TAG_LIST_ROUTE,
} from "../../routes/paths"
import { useUserStore } from "../../store/useUserStore"

interface NavigationButtonsProps {
  pad?: boolean
}

export const NavigationButtons = ({ pad = false }: NavigationButtonsProps) => {
  const isLoggedIn = useUserStore((state) => state.isLoggedIn)

  return (
    <>
      <Box justifyContent="space-between" display="flex" gap={2} width="100%">
        <Box>
          {isLoggedIn && (
            <>
              <PartyLink color="inherit" variant="text" to={INDEX_ROUTE}>
                Home
              </PartyLink>
              <PartyLink
                color="inherit"
                variant="text"
                to={PUBLIC_COMMUNITIES_ROUTE}
                preload="intent"
                preloadDelay={500}
              >
                Public Communities
              </PartyLink>
              <PartyLink
                color="inherit"
                variant="text"
                to={COMMUNITY_OPEN_ROUTE}
                preload="intent"
                preloadDelay={500}
              >
                My Communities
              </PartyLink>
              <PartyLink
                color="inherit"
                variant="text"
                to={PROFILE_ROUTE}
                preload="intent"
                preloadDelay={500}
              >
                Profile
              </PartyLink>
              <PartyLink
                color="inherit"
                variant="text"
                to={TAG_LIST_ROUTE}
                preload="intent"
                preloadDelay={500}
              >
                Tags
              </PartyLink>
            </>
          )}
        </Box>

        <Box sx={{ marginRight: pad ? "104px" : 0 }}>
          <LoginButton />
        </Box>
      </Box>
    </>
  )
}
