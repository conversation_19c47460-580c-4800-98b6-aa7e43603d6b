import MenuIcon from "@mui/icons-material/Menu"
import {
  AppB<PERSON>,
  Box,
  Container,
  Drawer,
  IconButton,
  Tool<PERSON>,
} from "@mui/material"
import { useCallback, useState } from "react"

import { useIsMobileStore } from "../../store/useIsMobileStore"

import { DrawerButtons } from "./DrawerButtons"
import { NavigationButtons } from "./NavigationButtons"

interface NavigationProps {
  pad?: boolean
}

export const Navigation = ({ pad = false }: NavigationProps) => {
  const sizeThresholdList = useIsMobileStore((state) => state.sizeThresholdList)
  const [open, setOpen] = useState(false)

  const handleToggleDrawer = useCallback(
    (newOpen: boolean) => () => {
      setOpen(newOpen)
    },
    [setOpen],
  )

  return (
    <Box sx={{ flexGrow: 1, height: "64px" }}>
      <AppBar position={sizeThresholdList.largeTablet ? "fixed" : "static"}>
        <Container maxWidth="xl">
          <Toolbar disableGutters>
            {sizeThresholdList.largeTablet && (
              <IconButton
                size="large"
                edge="start"
                color="inherit"
                aria-label="menu"
                onClick={handleToggleDrawer(!open)}
                sx={{ mr: 2 }}
              >
                <MenuIcon />
              </IconButton>
            )}
            {!sizeThresholdList.largeTablet && <NavigationButtons pad={pad} />}
            {sizeThresholdList.largeTablet && (
              <Drawer open={open} onClose={handleToggleDrawer(false)}>
                {<DrawerButtons toggleDrawer={handleToggleDrawer(false)} />}
              </Drawer>
            )}
          </Toolbar>
        </Container>
      </AppBar>
    </Box>
  )
}
