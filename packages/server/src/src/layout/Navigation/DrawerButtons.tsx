import {
  Box,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from "@mui/material"

import { LoginButton } from "../../components/LoginButton/LoginButton"
import { PartyListItemButton } from "../../components/elements/link/PartyListItemButton/PartyListItemButton"
import {
  COMMUNITY_OPEN_ROUTE,
  INDEX_ROUTE,
  PROFILE_ROUTE,
  PUBLIC_COMMUNITIES_ROUTE,
  TAG_LIST_ROUTE,
} from "../../routes/paths"
import { useUserStore } from "../../store/useUserStore"

interface DrawerButtonsProps {
  toggleDrawer: () => void
}
export const DrawerButtons = ({ toggleDrawer }: DrawerButtonsProps) => {
  const isLoggedIn = useUserStore((state) => state.isLoggedIn)
  const onClose = () => {
    toggleDrawer()
  }

  return (
    <Box
      onClick={onClose}
      display="flex"
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
      gap={2}
    >
      {isLoggedIn && (
        <>
          <List>
            <NavigationItem text="Home" route={INDEX_ROUTE} />
            <NavigationItem
              text="Public Communities"
              route={PUBLIC_COMMUNITIES_ROUTE}
            />
            <NavigationItem
              text="My Communities"
              route={COMMUNITY_OPEN_ROUTE}
            />
            <NavigationItem text="Profile" route={PROFILE_ROUTE} />
            <NavigationItem text="Tags" route={TAG_LIST_ROUTE} />
          </List>
          <Divider sx={{ width: "100%" }} />
        </>
      )}
      <LoginButton />
    </Box>
  )
}

interface NavigationItemProps {
  text: string
  icon?: React.ReactNode
  route: string
}
const NavigationItem = ({ text, icon, route }: NavigationItemProps) => {
  return (
    <ListItem key={text} disablePadding>
      <PartyListItemButton to={route} preload="intent" preloadDelay={500}>
        {icon && <ListItemIcon>{icon}</ListItemIcon>}
        <ListItemText primary={text} />
      </PartyListItemButton>
    </ListItem>
  )
}
