html,
body {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
}

:root {
  --spacing-q: 2px;
  --spacing-h: 4px;
  --spacing-1: 8px;
  --spacing-2: 16px;
  --spacing-3: 24px;
  --spacing-4: 32px;
  --spacing-5: 40px;
  --spacing-6: 48px;
  --spacing-7: 56px;
  --spacing-8: 64px;
  --spacing-9: 72px;
  --spacing-10: 80px;
  --spacing-11: 88px;
}


.layout {
  height: 100%;
  width: 100%;
  max-width: 1536px;
  margin: auto;
  position: relative;
}

.container {
  max-width: 1536px;
  margin: auto;
  position: relative;
  padding: 0 var(--spacing-2);
}
