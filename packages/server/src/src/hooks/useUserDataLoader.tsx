import { type User, getAuth, onAuthStateChanged } from "firebase/auth"
import { useCallback, useEffect, useState } from "react"

import { useUserStore } from "../store/useUserStore"
import { RouterOutput, trpc } from "../trpc/trpc"
import { auth as authStorage } from "../utils/access"
import { firebaseApp } from "../utils/firebase"

type UserDataLoaderOutput = RouterOutput["loggedInUser"]

export const useUserDataLoader = () => {
  const { setUserData, setUserLoaded } = useUserStore()
  const [isLoading, setIsLoading] = useState(true)
  const [user, setUser] = useState<User | null>(null)
  const [fetchCount, setFetchCount] = useState(0)
  const firebaseAuth = getAuth(firebaseApp)

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(firebaseAuth, (currentUser) => {
      setUser(currentUser)
      setIsLoading(false)
    })

    return () => unsubscribe()
  }, [firebaseAuth])

  useEffect(() => {
    const loadUserData = async () => {
      /*
      if (ENV_MODE === "dev") {
        // In development mode, load user data without authentication
        try {
          const res: UserDataLoaderOutput = await trpc.loggedInUser.query()
          if (res) {
            setUserData(res)
          }
        } catch (error) {
          console.error("Failed to load user data in dev mode:", error)
          setUserLoaded()
        }
        return
      }
       */

      if (user) {
        // User is authenticated, get Firebase ID token and load user data
        try {
          const idToken = await user.getIdToken()
          authStorage.authToken = idToken

          const res: UserDataLoaderOutput = await trpc.loggedInUser.query()
          if (res) {
            setUserData(res)
          }
        } catch (error) {
          console.error("Failed to get ID token or load user data:", error)
          setUserLoaded()
        }
      } else if (!isLoading) {
        // User is not authenticated and loading is complete
        setUserLoaded()
      }
    }

    if (!isLoading) {
      loadUserData()
    }
    // eslint-disable-next-line react-hooks-addons/no-unused-deps
  }, [user, isLoading, setUserData, setUserLoaded, fetchCount])

  const reFetch = useCallback(async () => {
    setFetchCount(fetchCount + 1)
  }, [setFetchCount, fetchCount])

  return { isLoading, user, reFetch }
}
