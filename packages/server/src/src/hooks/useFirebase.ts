import { getAuth } from "firebase/auth"
import { useEffect, useState } from "react"

import { firebaseApp } from "../utils/firebase"

export const useFirebase = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [getAccessTokenSilently, setGetAccessTokenSilently] = useState(
    () => async (): Promise<string | null> => null,
  )

  useEffect(() => {
    const firebaseAuth = getAuth(firebaseApp)
    const unsubscribe = firebaseAuth.onAuthStateChanged(async (user) => {
      if (user) {
        setIsAuthenticated(true)
        setGetAccessTokenSilently(() => async () => user.getIdToken())
      } else {
        setIsAuthenticated(false)
        setGetAccessTokenSilently(() => async () => null)
      }
    })

    return () => unsubscribe()
  }, [])

  return { isAuthenticated, getAccessTokenSilently }
}
