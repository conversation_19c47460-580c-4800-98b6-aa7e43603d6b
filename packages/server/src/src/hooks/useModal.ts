import { useCallback, useState } from "react"

interface Modal {
  id: string
  isOpen: boolean
}

type ModalList = Record<string, Modal>

export const useModal = () => {
  const [modalList, setModalList] = useState<ModalList>({})

  const createModal = useCallback(
    (name: string) => {
      setModalList((prev) => ({
        ...prev,
        [name]: { id: name, isOpen: false },
      }))
    },
    [setModalList, modalList],
  )

  const modal = useCallback(
    (name: string) => {
      if (modalList[name]) {
        return modalList[name]
      }

      return null
    },
    [modalList],
  )

  const open = useCallback(
    (name: string) => {
      setModalList((prev) => ({
        ...prev,
        [name]: { id: name, isOpen: true },
      }))
    },
    [setModalList, modalList],
  )

  return { modal, createModal }
}
