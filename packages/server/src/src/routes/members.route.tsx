import { createRoute } from "@tanstack/react-router"

import { LoadingPage } from "../pages/LoadingPage/LoadingPage"
import { MembersPage } from "../pages/membersPage/MembersPage"

import { communityRootRoute } from "./community.root.route"
import { handleLoaderErrors } from "./handleLoaderErrors"
import { PART_COMMUNITY_USERS_ROUTE } from "./paths"

export const membersRoute = createRoute({
  getParentRoute: () => communityRootRoute,
  path: PART_COMMUNITY_USERS_ROUTE,
  pendingComponent: LoadingPage,
  pendingMs: 300,
  loader: async ({ context: { trpc }, params: { communityId } }) => {
    try {
      return await trpc.communityUserList.query({
        communityId: parseInt(communityId),
      })
    } catch (error) {
      return handleLoaderErrors(
        "You don't have access to community users",
        error,
      )
    }
  },
  component: MembersPage,
})
