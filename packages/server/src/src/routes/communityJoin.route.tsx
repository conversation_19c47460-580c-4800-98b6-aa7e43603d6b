import { createRoute } from "@tanstack/react-router"
import { z } from "zod"

import { LoadingPage } from "../pages/LoadingPage/LoadingPage"
import { JoinCommunityPage } from "../pages/joinCommunityPage/JoinCommunityPage"

import { handleLoaderErrors } from "./handleLoaderErrors"
import { indexRootRoute } from "./index.route"
import { COMMUNITY_JOIN_ROUTE } from "./paths"

const communityJoinSchema = z.object({
  code: z.string(),
})

export const communityJoinRoute = createRoute({
  getParentRoute: () => indexRootRoute,
  validateSearch: (search) => communityJoinSchema.parse(search),
  path: COMMUNITY_JOIN_ROUTE,
  loaderDeps: ({ search: { code } }) => ({ code }),
  loader: async ({ context: { trpc }, deps: { code } }) => {
    try {
      return await trpc.joinByInvitePrejoin.query({
        code,
      })
    } catch (error) {
      return handleLoaderErrors("Can't join!", error)
    }
  },
  pendingComponent: LoadingPage,
  pendingMs: 300,
  component: JoinCommunityPage,
})
