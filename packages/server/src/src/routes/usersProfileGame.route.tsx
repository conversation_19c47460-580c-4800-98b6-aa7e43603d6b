import { createRoute } from "@tanstack/react-router"
import { z } from "zod"

import { USER_PROFILE_STALE_TIME } from "../config/routes"
import { LoadingPage } from "../pages/LoadingPage/LoadingPage"
import { UsersGamePage } from "../pages/usersGamePage/UsersGamePage"

import { handleLoaderErrors } from "./handleLoaderErrors"
import { indexRootRoute } from "./index.route"
import { PROFILE_GAME_ROUTE } from "./paths"

const usersProfileGameSchema = z.object({
  search: z.string().optional(),
})

export const usersProfileGameRoute = createRoute({
  getParentRoute: () => indexRootRoute,
  validateSearch: (search) => usersProfileGameSchema.parse(search),
  path: PROFILE_GAME_ROUTE,
  staleTime: USER_PROFILE_STALE_TIME,
  pendingComponent: LoadingPage,
  pendingMs: 300,
  loader: async ({ context: { trpc }, params: { gameId } }) => {
    try {
      return await trpc.getMyGameInfo.query({
        gameId: parseInt(gameId),
      })
    } catch (error) {
      return handleLoaderErrors("No game information available", error)
    }
  },
  component: UsersGamePage,
})
