import { createRoute } from "@tanstack/react-router"

import { indexRootRoute } from "./index.route"
import { LOGGEDIN_ROUTE, LOGGEDOUT_ROUTE } from "./paths"

export const loggedinRoute = createRoute({
  getParentRoute: () => indexRootRoute,
  path: LOGGEDIN_ROUTE,
  component: function Index() {
    return <div>You logged In</div>
  },
})

export const loggedoutRoute = createRoute({
  getParentRoute: () => indexRootRoute,
  path: LOGGEDOUT_ROUTE,
  component: function Index() {
    return <div>You logged Out!</div>
  },
})
