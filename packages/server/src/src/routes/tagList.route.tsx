import { createRoute } from "@tanstack/react-router"

import { TAG_LIST_STALE_TIME } from "../config/routes"
import { LoadingPage } from "../pages/LoadingPage/LoadingPage"
import { TagListPage } from "../pages/tagListPage/TagListPage"

import { handleLoaderErrors } from "./handleLoaderErrors"
import { indexRootRoute } from "./index.route"
import { TAG_LIST_ROUTE } from "./paths"

export const tagListRoute = createRoute({
  getParentRoute: () => indexRootRoute,
  path: TAG_LIST_ROUTE,
  staleTime: TAG_LIST_STALE_TIME,
  pendingComponent: LoadingPage,
  pendingMs: 300,
  loader: async ({ context: { trpc } }) => {
    try {
      return await trpc.tagList.query()
    } catch (error) {
      return handleLoaderErrors("Tag information not available", error)
    }
  },
  component: TagListPage,
})
