import {
  Outlet,
  createRoute,
  redirect,
  useMatchRoute,
} from "@tanstack/react-router"
import { TanStackRouterDevtools } from "@tanstack/router-devtools"

import { Layout } from "../layout/Layout/Layout"
import { Navigation } from "../layout/Navigation/Navigation"
import { IndexPage } from "../pages/indexPage/IndexPage"

import { INDEX_ROUTE, TAG_LIST_ROUTE } from "./paths"
import { rootRoute } from "./root"

export const indexRootRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: INDEX_ROUTE,
  beforeLoad: ({ context: { userData }, location }) => {
    if (
      (!userData || !userData.isLoggedIn) &&
      location.pathname !== INDEX_ROUTE &&
      location.pathname !== TAG_LIST_ROUTE
    ) {
      throw redirect({
        to: INDEX_ROUTE,
      })
    }
  },
  component: function Index() {
    const matchRoute = useMatchRoute()
    const isRootPath = matchRoute({ to: INDEX_ROUTE })

    return (
      <>
        <Navigation />
        <Layout>
          {isRootPath && <IndexPage />}
          {!isRootPath && <Outlet />}
        </Layout>
        {ENV_MODE === "dev" && <TanStackRouterDevtools />}
      </>
    )
  },
})
