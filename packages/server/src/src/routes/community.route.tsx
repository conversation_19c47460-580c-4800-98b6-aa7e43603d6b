import { createRoute, redirect } from "@tanstack/react-router"
import { z } from "zod"

import { COMMUNITIES_STALE_TIME, COMMUNITY_STALE_TIME } from "../config/routes"
import { LoadingPage } from "../pages/LoadingPage/LoadingPage"
import { CommunitiesPage } from "../pages/communitiesPage/CommunitiesPage"
import { CommunityPage } from "../pages/communityPage/CommunityPage"
import { EditCommunityPage } from "../pages/editCommunityPage/EditCommunityPage"
import { PublicCommunitiesPage } from "../pages/publicCommunitiesPage/PublicCommunitiesPage"

import { communityRootRoute } from "./community.root.route"
import { handleLoaderErrors } from "./handleLoaderErrors"
import { indexRootRoute } from "./index.route"
import {
  COMMUNITY_OPEN_ROUTE,
  INDEX_ROUTE,
  PART_COMMUNITY_EDIT_ROUTE,
  PART_COMMUNITY_ROUTE,
  PUBLIC_COMMUNITIES_ROUTE,
} from "./paths"

export const communityListRoute = createRoute({
  getParentRoute: () => indexRootRoute,
  path: COMMUNITY_OPEN_ROUTE,
  staleTime: COMMUNITIES_STALE_TIME,
  loader: async ({ context: { trpc } }) => {
    try {
      return await trpc.communityList.query()
    } catch (error) {
      return handleLoaderErrors("Can't find your communities", error)
    }
  },
  pendingComponent: LoadingPage,
  pendingMs: 300,
  component: CommunitiesPage,
})

export const publicCommunitiesRoute = createRoute({
  getParentRoute: () => indexRootRoute,
  staleTime: COMMUNITIES_STALE_TIME,
  path: PUBLIC_COMMUNITIES_ROUTE,
  loader: async ({ context: { trpc } }) => {
    try {
      return await trpc.publicCommunityList.query()
    } catch (error) {
      return handleLoaderErrors("Can't find public communities", error)
    }
  },
  pendingComponent: LoadingPage,
  pendingMs: 300,
  component: PublicCommunitiesPage,
})

const communityOpenSchema = z.object({
  tab: z.string().optional(),
})

export const communityOpenRoute = createRoute({
  validateSearch: (search) => communityOpenSchema.parse(search),
  getParentRoute: () => communityRootRoute,
  path: PART_COMMUNITY_ROUTE,
  staleTime: COMMUNITY_STALE_TIME,
  beforeLoad: ({ context: { userData } }) => {
    if (!userData || !userData.isLoggedIn) {
      throw redirect({
        to: INDEX_ROUTE,
      })
    }
  },
  loader: async ({ context: { trpc }, params: { communityId } }) => {
    try {
      return await trpc.communityExpanded2.query({
        communityId: parseInt(communityId),
      })
    } catch (error) {
      return handleLoaderErrors("Can't find community info", error)
    }
  },
  pendingComponent: LoadingPage,
  pendingMs: 300,
  component: CommunityPage,
})

export const communityEditRoute = createRoute({
  getParentRoute: () => communityRootRoute,
  path: PART_COMMUNITY_EDIT_ROUTE,
  pendingComponent: LoadingPage,
  pendingMs: 300,
  component: EditCommunityPage,
})
