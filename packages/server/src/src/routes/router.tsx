import { Router<PERSON>rovider, createRouter } from "@tanstack/react-router"

import { GeneralErrorPage } from "../pages/generalErrorPage/GeneralErrorPage"
import { useGameStore } from "../store/useGamesStore"
import { useUserStore } from "../store/useUserStore"
import { trpc } from "../trpc/trpc"
import { firebaseApp } from "../utils/firebase"

import { communityRootRoute } from "./community.root.route"
import {
  communityEditRoute,
  communityListRoute,
  communityOpenRoute,
  publicCommunitiesRoute,
} from "./community.route"
import { communityJoinRoute } from "./communityJoin.route"
import { createCommunityRoute } from "./createCommunity.route"
import { gameRoute } from "./game.route"
import { gamesRoute } from "./games.route"
import { indexRootRoute } from "./index.route"
import { loggedinRoute, loggedoutRoute } from "./login.route"
import { memberRoute } from "./member.route"
import { membersRoute } from "./members.route"
import { rootRoute } from "./root"
import { tagListRoute } from "./tagList.route"
import { usersProfileRoute } from "./usersProfile.route"
import { usersProfileGameRoute } from "./usersProfileGame.route"

const routeTree = rootRoute.addChildren([
  indexRootRoute.addChildren([
    communityListRoute,
    loggedinRoute,
    loggedoutRoute,
    usersProfileRoute,
    publicCommunitiesRoute,
    createCommunityRoute,
    communityJoinRoute,
    usersProfileGameRoute,
    tagListRoute,
  ]),
  communityRootRoute.addChildren([
    communityOpenRoute,
    memberRoute,
    membersRoute,
    gameRoute,
    gamesRoute,
    communityEditRoute,
  ]),
])

export const router = createRouter({
  routeTree,
  context: {
    gameStore: null,
    auth: null,
    trpc,
    userData: null,
  },
  defaultNotFoundComponent: GeneralErrorPage,
})

declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router
  }
}

export const RouterProviderWithContext = () => {
  const auth = firebaseApp
  const userData = useUserStore()
  const gameStore = useGameStore()

  return (
    <RouterProvider router={router} context={{ auth, userData, gameStore }} />
  )
}
