// ================== PUBLIC ROUTES

export const INDEX_ROUTE = "/"

export const PUBLIC_COMMUNITIES_ROUTE = "/public"

export const LOGGEDIN_ROUTE = "/loggedin"

export const LOGGEDOUT_ROUTE = "/loggedout"

export const PROFILE_ROUTE = "/profile"

export const TAG_LIST_ROUTE = "/tags"

export const PROFILE_GAME_ROUTE = "/profile/game/$gameId"

export const COMMUNITY_OPEN_ROUTE = "/communities"

export const CREATE_COMMUNITY_ROUTE = "/create-community"

export const COMMUNITY_JOIN_ROUTE = `/join`

// ================== COMMUNITY ROUTES
export const COMMUNITIES_ROOT_ROUTE = "/community/$communityId"

export const PART_COMMUNITY_ROUTE = "/info"
export const COMMUNITY_ROUTE = `${COMMUNITIES_ROOT_ROUTE}${PART_COMMUNITY_ROUTE}`

export const PART_GAME_ROUTE = "/game/$gameId"
export const GAME_ROUTE = `${COMMUNITIES_ROOT_ROUTE}${PART_GAME_ROUTE}`

export const PART_GAMES_ROUTE = "/games"
export const GAMES_ROUTE = `${COMMUNITIES_ROOT_ROUTE}${PART_GAMES_ROUTE}`

export const PART_COMMUNITY_USER_ROUTE = "/user/$userId"
export const COMMUNITY_USER_ROUTE = `${COMMUNITIES_ROOT_ROUTE}${PART_COMMUNITY_USER_ROUTE}`

export const PART_COMMUNITY_USERS_ROUTE = "/users"
export const COMMUNITY_USERS_ROUTE = `${COMMUNITIES_ROOT_ROUTE}${PART_COMMUNITY_USERS_ROUTE}`

export const PART_COMMUNITY_PROFILE_ROUTE = "/profile"
export const COMMUNITY_PROFILE_ROUTE = `${COMMUNITIES_ROOT_ROUTE}${PART_COMMUNITY_PROFILE_ROUTE}`

export const PART_COMMUNITY_EDIT_ROUTE = "/edit"
export const COMMUNITY_EDIT_ROUTE = `${COMMUNITIES_ROOT_ROUTE}${PART_COMMUNITY_EDIT_ROUTE}`
