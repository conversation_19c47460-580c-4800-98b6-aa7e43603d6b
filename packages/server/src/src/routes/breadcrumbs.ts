import { BreadcrumbLink } from "../types/global"

import {
  COMMUNITY_OPEN_ROUTE,
  COMMUNITY_PROFILE_ROUTE,
  COMMUNITY_ROUTE,
  COMMUNITY_USERS_ROUTE,
  COMMUNITY_USER_ROUTE,
  GAMES_ROUTE,
  GAME_ROUTE,
  INDEX_ROUTE,
  PROFILE_GAME_ROUTE,
  PROFILE_ROUTE,
  PUBLIC_COMMUNITIES_ROUTE,
  TAG_LIST_ROUTE,
} from "./paths"

type BreadcrumbDataList = Record<string, Record<string, string>>

export const createRoutes = (
  setBreadcrumbs: (a: BreadcrumbLink[]) => void,
  current: string,
  list: BreadcrumbDataList,
  values: Record<string, string> = {},
) => {
  try {
    const replaceKeys = Object.keys(values)

    const crumbs = Object.entries(list).map((item) => {
      const activeItem = breadcrumbs[item[0]]
      if (!activeItem) {
        throw new Error("Breadcrumb not found, please check naming!")
      }
      const crumb = {
        ...activeItem,
        name: replaceKeys.reduce(
          (name, repacement) => name.replaceAll(repacement, values[repacement]),
          activeItem.name,
        ),
        props: item,
        current: current === item[0],
      }

      return crumb
    })

    setBreadcrumbs(crumbs)
  } catch (e) {
    console.error(e)
  }
}

export const breadcrumbs: Record<string, BreadcrumbLink> = {
  gameRoute: {
    name: "$game",
    to: GAME_ROUTE,
  },
  gamesRoute: {
    name: "Games",
    to: GAMES_ROUTE,
  },
  communityRoute: {
    name: "$community",
    to: COMMUNITY_ROUTE,
  },
  communitiesRoute: {
    name: "My Communities",
    to: COMMUNITY_OPEN_ROUTE,
  },
  publicCommunitiesRoute: {
    name: "Public Communities",
    to: PUBLIC_COMMUNITIES_ROUTE,
  },
  userRoute: {
    name: "$user",
    to: COMMUNITY_USER_ROUTE,
  },
  usersRoute: {
    name: "Members",
    to: COMMUNITY_USERS_ROUTE,
  },
  indexRoute: {
    name: "Home",
    to: INDEX_ROUTE,
  },
  tagListRoute: {
    name: "Tags",
    to: TAG_LIST_ROUTE,
  },
  profileRoute: {
    name: "Profile",
    to: PROFILE_ROUTE,
  },
  profileGameRoute: {
    name: "$game",
    to: PROFILE_GAME_ROUTE,
  },
  communityProfile: {
    name: "Community profile",
    to: COMMUNITY_PROFILE_ROUTE,
  },
}
