import { createRoute } from "@tanstack/react-router"
import { z } from "zod"

import { COMMUNITY_GAME_STALE_TIME } from "../config/routes"
import { LoadingPage } from "../pages/LoadingPage/LoadingPage"
import { GamePage } from "../pages/gamePage/GamePage"

import { communityRootRoute } from "./community.root.route"
import { handleLoaderErrors } from "./handleLoaderErrors"
import { PART_GAME_ROUTE } from "./paths"

const gameRouteSearchSchema = z.object({
  userId: z.number().optional(),
  hideOthers: z.boolean().optional(),
  search: z.string().optional(),
})

export const gameRoute = createRoute({
  getParentRoute: () => communityRootRoute,
  validateSearch: (search) => gameRouteSearchSchema.parse(search),
  path: PART_GAME_ROUTE,
  staleTime: COMMUNITY_GAME_STALE_TIME,
  loader: async ({ context: { trpc }, params: { gameId, communityId } }) => {
    try {
      return await trpc.communityGameExtended2.query({
        gameId: parseInt(gameId),
        communityId: parseInt(communityId),
      })
    } catch (error) {
      return handleLoaderErrors("Game not found", error)
    }
  },
  pendingComponent: LoadingPage,
  pendingMs: 300,
  component: GamePage,
})
