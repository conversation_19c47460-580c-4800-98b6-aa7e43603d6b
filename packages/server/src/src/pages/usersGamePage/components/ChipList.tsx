import { Box, Chip } from "@mui/material"
import React from "react"

import { IGameViewTag } from "../../../types/tRPC.types"

interface ChipListProps {
  tagList: IGameViewTag[]
}

const sortTags = (t1: IGameViewTag, t2: IGameViewTag) => {
  if ((t1.typeId ?? 0) > (t2.typeId ?? 0)) {
    return 1
  }

  if ((t1.typeId ?? 0) < (t2.typeId ?? 0)) {
    return -1
  }

  return t1.title < t2.title ? -1 : 1
}
export const ChipList = ({ tagList }: ChipListProps) => {
  return (
    <Box
      padding={2}
      gap={0.5}
      display="flex"
      flexDirection="row"
      flexWrap="wrap"
    >
      {tagList.sort(sortTags).map((tag) => (
        <Chip
          sx={{
            backgroundColor: `#${tag.color}`,
          }}
          title={`${tag.type}: ${tag.title}`}
          key={tag.title}
          component="div"
          label={tag.title}
          variant="outlined"
          onClick={() => {}}
        />
      ))}
    </Box>
  )
}
