.card {
  width: 250px;
  min-width: 250px;
  height: 250px;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  opacity: 1;
}

.cardContent {
  background-color: rgba(256, 256, 256, 0.8);
  position: absolute;
  bottom: 0;
  width: 100%;
  box-sizing: border-box;
}

.newGame {
  position: absolute;
  top: 0px;
  left: 0px;
}

.bggLink {
  position: absolute;
  top: 0px;
  right: 0px;
}

.bggRating {
  position: absolute;
  top:0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.base {
  box-shadow: 0px 2px 4px -1px rgba(0,0,0,0.2),0px 4px 5px 0px rgba(25, 118, 210,0.9),0px 1px 10px 0px rgba(0,0,0,0.12)
}