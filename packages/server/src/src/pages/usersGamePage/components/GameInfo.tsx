import { Box, Grid2, Typography } from "@mui/material"
import { useCallback } from "react"

import { IUserProfileGameDetails } from "../../../types/tRPC.types"
import * as styles from "../gamePage.css"

import { InfoRow } from "./InfoRow"

interface GameInfoProps {
  game: IUserProfileGameDetails
}
export const GameInfo = ({ game }: GameInfoProps) => {
  const getPlayerCount = useCallback(() => {
    const recommended: string[] = []
    const best: string[] = []
    game.players.stats?.forEach((count) => {
      const title = !count[2] ? String(count[0]) : `${count[0]}+`
      if (count[1] === 1) {
        best.push(title)
      }
      if (count[1] === 2) {
        recommended.push(title)
      }
    })
    return `Best: ${best.join(", ")} Rec.: ${recommended.join(", ")}`
  }, [game.players.stats])

  return (
    <>
      <Typography variant="subtitle1" color="textSecondary">
        {"Game info"}
      </Typography>
      <Box className={styles.infoRowCell}>
        <Grid2 container spacing={2} columns={2}>
          <InfoRow title="Title:" value={game.title} />
          <InfoRow
            title="Box player count:"
            value={
              game.players.box.min === game.players.box.max
                ? String(game.players.box.min)
                : `${game.players.box.min} - ${game.players.box.max}`
            }
          />
          <InfoRow title="BGG player count:" value={getPlayerCount()} />
          <InfoRow
            title="Box play length:"
            value={
              game.length.box.min === game.length.box.max
                ? String(game.length.box.min)
                : `${game.length.box.min} - ${game.length.box.max}`
            }
          />
          <InfoRow title="Age:" value={`${game.age}+`} />
        </Grid2>
      </Box>
    </>
  )
}
