import classnames from "classnames"

import { GameThumbnail } from "../../../components/GameThumbnail/GameThumbnail"
import { IUserProfileGameExpansion } from "../../../types/tRPC.types"

import * as styles from "./expansionThumbnail.css"

interface ExpansionThumbnailProps {
  expansion: IUserProfileGameExpansion
}
export const GameThumbnailWrapper = ({
  expansion,
}: ExpansionThumbnailProps) => {
  return (
    <GameThumbnail
      className={classnames(styles.card, {
        [styles.base]:
          expansion.type === "base" || expansion.type === "base-expansion",
      })}
      game={expansion}
    />
  )
}
