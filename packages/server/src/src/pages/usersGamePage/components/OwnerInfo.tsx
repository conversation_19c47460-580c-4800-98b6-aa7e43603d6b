import { Box, Grid2, Typography } from "@mui/material"
import dayjs from "dayjs"
import localizedFormat from "dayjs/plugin/localizedFormat"

import { IUserProfileGameData } from "../../../types/tRPC.types"
import * as styles from "../gamePage.css"

import { InfoRow } from "./InfoRow"

interface OwnerInfoProps {
  userSelected: IUserProfileGameData
  id?: number
}
export const OwnerInfo = ({ userSelected }: OwnerInfoProps) => {
  dayjs.extend(localizedFormat)

  return (
    <>
      <Typography variant="subtitle1" color="textSecondary">
        {"My info"}
      </Typography>
      <Box className={styles.infoRowCell}>
        {userSelected !== null && (
          <Grid2 container spacing={2} columns={2}>
            {(userSelected.rating ?? 0) > 0 && (
              <InfoRow title="Rating" value={String(userSelected.rating)} />
            )}
            {userSelected.lastPlay && (
              <InfoRow
                title="Last Play"
                value={dayjs(userSelected.lastPlay).format("ll")}
              />
            )}
            {(userSelected.playCount ?? 0) > 0 && (
              <InfoRow
                title="Play count"
                value={String(userSelected.playCount ?? 0)}
              />
            )}
            <InfoRow
              title="Can teach"
              value={userSelected.willTeach ? "Yes" : "No"}
            />
            {userSelected.portability && (
              <InfoRow title="Portability" value={userSelected.portability} />
            )}
            {userSelected.events && (
              <InfoRow title="Events" value={userSelected.events} />
            )}
          </Grid2>
        )}
      </Box>
    </>
  )
}
