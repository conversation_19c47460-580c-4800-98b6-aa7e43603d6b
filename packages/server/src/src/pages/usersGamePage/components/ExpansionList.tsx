import { Box, FormControl, TextField, Typography } from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import debounce from "debounce"
import {
  ChangeEvent,
  KeyboardEvent,
  useCallback,
  useMemo,
  useState,
} from "react"

import { PROFILE_GAME_ROUTE } from "../../../routes/paths"
import { usersProfileGameRoute } from "../../../routes/usersProfileGame.route"
import { IUserProfileGameExpansion } from "../../../types/tRPC.types"

import { GameThumbnailWrapper } from "./GameThumbnailWrapper"

interface ExpansionListProps {
  expansions: IUserProfileGameExpansion[]
}

const sort = (s1: IUserProfileGameExpansion, s2: IUserProfileGameExpansion) => {
  if (
    (s1.type === "base" || s1.type === "base-expansion") &&
    s2.type !== "base" &&
    s2.type !== "base-expansion"
  ) {
    return -1
  }

  if (
    (s2.type === "base" || s2.type === "base-expansion") &&
    s1.type !== "base" &&
    s1.type !== "base-expansion"
  ) {
    return 1
  }
  return s1.bggId > s2.bggId ? 1 : -1
}

export const ExpansionList = ({ expansions }: ExpansionListProps) => {
  const { search } = usersProfileGameRoute.useSearch()
  const navigate = useNavigate()
  const linkParams = usersProfileGameRoute.useParams()
  const [searchText, setSearchText] = useState(search)

  const onSearch = useCallback(
    (search: string) => {
      navigate({
        to: PROFILE_GAME_ROUTE,
        params: {
          gameId: linkParams.gameId,
        },
        search: {
          search: search,
        },
      })
    },
    [linkParams],
  )

  const onSearchDelayed = useMemo(() => debounce(onSearch, 100), [onSearch])

  const onSearchType = (event: ChangeEvent<HTMLInputElement>) => {
    onSearchDelayed(event.target.value)
    setSearchText(event.target.value)
  }

  const onKeyDown = (e: KeyboardEvent<HTMLDivElement>) => {
    if (e.key === "Escape") {
      setSearchText("")
      onSearch("")
    }
  }

  const searchLower = search?.toLowerCase() ?? ""

  return (
    <Box>
      <Box mb={2} display="flex" justifyContent="space-between">
        <Typography variant="h6">Owned game items</Typography>
        <FormControl>
          <TextField
            label="Search"
            variant="outlined"
            value={searchText}
            onChange={onSearchType}
            onKeyDown={onKeyDown}
          />
        </FormControl>
      </Box>
      <Box display="flex" gap={2} flexWrap="wrap" justifyContent="center">
        {expansions
          .sort(sort)
          .filter((expansion) =>
            expansion.title.toLowerCase().includes(searchLower),
          )
          .map((expansion: IUserProfileGameExpansion) => (
            <GameThumbnailWrapper key={expansion.id} expansion={expansion} />
          ))}
      </Box>
    </Box>
  )
}
