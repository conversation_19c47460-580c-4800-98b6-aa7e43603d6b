import { Box, Button, Grid2, Typo<PERSON> } from "@mui/material"
import { useRouter } from "@tanstack/react-router"
import { useEffect } from "react"

import { BggLink } from "../../components/BggLink/BggLink"
import { TitleRow } from "../../components/titleRow/TitleRow"
import { GAME_IMAGES } from "../../config/images"
import { createRoutes } from "../../routes/breadcrumbs"
import { usersProfileGameRoute } from "../../routes/usersProfileGame.route"
import { useBreadcrumbsStore } from "../../store/useBreadcrumbsStore"
import { useIsMobileStore } from "../../store/useIsMobileStore"

import { ChipList } from "./components/ChipList"
import { ExpansionList } from "./components/ExpansionList"
import { GameInfo } from "./components/GameInfo"
import { OwnerInfo } from "./components/OwnerInfo"
import * as styles from "./gamePage.css"

export const UsersGamePage = () => {
  const game = usersProfileGameRoute.useLoaderData()
  const setBreadcrumbs = useBreadcrumbsStore((state) => state.setBreadcrumbs)
  const sizeThresholdList = useIsMobileStore((state) => state.sizeThresholdList)
  const router = useRouter()

  if (!game) return null

  useEffect(() => {
    createRoutes(
      setBreadcrumbs,
      "gameRoute",
      {
        indexRoute: {},
        profileRoute: {},
        profileGameRoute: {},
      },
      {
        $game: game.game.title,
      },
    )
  }, [game.game.title])

  const back = () => router.history.back()

  const isLargeTablet = sizeThresholdList.largeTablet

  const isMobile = sizeThresholdList.mobile

  return (
    <>
      <TitleRow title={game.game.title}>
        <Button variant="outlined" onClick={back}>
          Back
        </Button>
      </TitleRow>
      <Box padding={2} pl={4}>
        <Box pt={2}>
          <Grid2 container columns={10} spacing={2}>
            <Grid2
              sx={{ width: "250px" }}
              size={isLargeTablet ? (isMobile ? 10 : 5) : 2}
            >
              <Box className={styles.imageBox}>
                <img
                  src={`${ENV_IMAGE_CDN}${GAME_IMAGES}/${game.game.id}.jpg`}
                />
                <Box className={styles.link}>
                  <BggLink bggId={game.game.bggId} />
                  {game.game.average && (
                    <Box className={styles.bggRating}>
                      <Typography fontWeight={600}>
                        {Math.round(game.game.average * 10) / 10}
                      </Typography>
                    </Box>
                  )}
                </Box>
              </Box>
            </Grid2>
            <Grid2 size={isLargeTablet ? (isMobile ? 10 : 5) : 2}>
              <GameInfo game={game.game} />
            </Grid2>
            <Grid2 size={isLargeTablet ? (isMobile ? 10 : 5) : 2}>
              <OwnerInfo userSelected={game.myData} />
            </Grid2>
            <Grid2 size={isLargeTablet ? (isMobile ? 10 : 5) : 2}>
              <Typography variant="subtitle1" color="textSecondary">
                Tags
              </Typography>
              <Box className={styles.infoRowCell}>
                <ChipList tagList={game.tags} />
              </Box>
            </Grid2>
          </Grid2>
        </Box>
        {game.expansions.length > 0 && (
          <Box pt={2}>
            <ExpansionList expansions={game.expansions} />
          </Box>
        )}
      </Box>
    </>
  )
}
