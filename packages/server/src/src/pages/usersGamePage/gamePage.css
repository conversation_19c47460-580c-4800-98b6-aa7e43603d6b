.infoRowCell {
    max-height: 350px;
}

.infoUsers {
    composes: infoRowCell;
    overflow: auto;
}


.imageBox {
    composes: infoRowCell;
    width: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}


.infoTitle {
}

.infoValue {
}

.link {
    position: absolute;
    top: var(--spacing-1);
    right: var(--spacing-1);
}

.bggRating {
    position: absolute;
    top:0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none;
}