import { Box, FormControl, TextField, Typography } from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import debounce from "debounce"
import {
  ChangeEvent,
  KeyboardEvent,
  useCallback,
  useMemo,
  useState,
} from "react"

import { ICAvatarUser } from "../../../components/UserAvatar/UserAvatar"
import { gameRoute } from "../../../routes/game.route"
import { GAME_ROUTE } from "../../../routes/paths"
import {
  IGameViewExpansion,
  IGameViewExpansions,
} from "../../../types/tRPC.types"

import { GameThumbnailWrapper } from "./GameThumbnailWrapper"

interface ExpansionListProps {
  expansions: IGameViewExpansions
  users: ICAvatarUser[]
  onViewUser: (user?: number) => void
}

const sort = (s1: IGameViewExpansion, s2: IGameViewExpansion) => {
  if (
    (s1.type === "base" || s1.type === "base-expansion") &&
    s2.type !== "base" &&
    s2.type !== "base-expansion"
  ) {
    return -1
  }

  if (
    (s2.type === "base" || s2.type === "base-expansion") &&
    s1.type !== "base" &&
    s1.type !== "base-expansion"
  ) {
    return 1
  }
  return s1.bggId > s2.bggId ? 1 : -1
}

export const ExpansionList = ({
  expansions,
  onViewUser,
  users,
}: ExpansionListProps) => {
  const { userId, hideOthers, search } = gameRoute.useSearch()
  const navigate = useNavigate()
  const linkParams = gameRoute.useParams()
  const [searchText, setSearchText] = useState(search)

  const check = userId && hideOthers

  const onSearch = useCallback(
    (search: string) => {
      navigate({
        to: GAME_ROUTE,
        params: {
          communityId: linkParams.communityId,
          gameId: linkParams.gameId,
        },
        search: {
          userId: userId,
          hideOthers: hideOthers,
          search: search,
        },
      })
    },
    [linkParams, userId, hideOthers],
  )

  const onSearchDelayed = useMemo(() => debounce(onSearch, 100), [onSearch])

  const onSearchType = (event: ChangeEvent<HTMLInputElement>) => {
    onSearchDelayed(event.target.value)
    setSearchText(event.target.value)
  }

  const onKeyDown = (e: KeyboardEvent<HTMLDivElement>) => {
    if (e.key === "Escape") {
      setSearchText("")
      onSearch("")
    }
  }

  const searchLower = search?.toLowerCase() ?? ""

  return (
    <Box>
      <Box mb={2} display="flex" justifyContent="space-between">
        <Typography variant="h6">Owned game items</Typography>
        <FormControl>
          <TextField
            label="Search"
            variant="outlined"
            value={searchText}
            onChange={onSearchType}
            onKeyDown={onKeyDown}
          />
        </FormControl>
      </Box>
      <Box display="flex" gap={2} flexWrap="wrap" justifyContent="center">
        {expansions
          .sort(sort)
          .filter((expansion) =>
            expansion.title.toLowerCase().includes(searchLower),
          )
          .filter((expansion) =>
            check ? expansion.users?.find((user) => user == userId) : true,
          )
          .map((expansion: IGameViewExpansion) => (
            <GameThumbnailWrapper
              users={users}
              onViewUser={onViewUser}
              key={expansion.id}
              expansion={expansion}
            />
          ))}
      </Box>
    </Box>
  )
}
