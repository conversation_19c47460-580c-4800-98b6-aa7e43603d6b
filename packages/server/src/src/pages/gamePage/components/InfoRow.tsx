import { Box, Grid2, Typography } from "@mui/material"

interface InfoRowProps {
  title: string
  value: string
}
export const InfoRow = ({ title, value }: InfoRowProps) => {
  return (
    <>
      <Grid2 size={1}>
        <Box display="flex" justifyContent="flex-end" alignItems="center">
          <Typography
            lineHeight={1.5}
            variant="body1"
            fontWeight={600}
            textAlign="right"
          >
            {title}
          </Typography>
        </Box>
      </Grid2>
      <Grid2 size={1}>
        <Box display="flex" justifyContent="flex-start" alignItems="center">
          <Typography variant="body1" lineHeight={1.5}>
            {value}
          </Typography>
        </Box>
      </Grid2>
    </>
  )
}
