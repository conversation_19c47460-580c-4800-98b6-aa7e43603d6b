import { Box, Checkbox, FormControlLabel, Typography } from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import { useCallback } from "react"

import { gameRoute } from "../../../routes/game.route"
import { COMMUNITY_USER_ROUTE, GAME_ROUTE } from "../../../routes/paths"
import * as styles from "../gamePage.css"

import { ICChipUser, UserChips } from "./UserChips"

interface SelectOwnerProps {
  search: {
    hideOthers?: boolean
    userId?: number
  }
  users: ICChipUser[]
  onViewUser: (user?: number) => void
  communityId: number
}
export const SelectOwner = ({
  search,
  users,
  onViewUser,
  communityId,
}: SelectOwnerProps) => {
  const navigate = useNavigate()
  const params = gameRoute.useParams()

  const onChange = useCallback(
    () =>
      navigate({
        to: GAME_ROUTE,
        params: params,
        search: {
          userId: search.userId,
          hideOthers: !search.hideOthers,
        },
      }),
    [navigate, search, params],
  )

  const onOpenUserProfile = useCallback(
    (id: number) =>
      navigate({
        to: COMMUNITY_USER_ROUTE,
        params: {
          userId: String(id),
          communityId: String(communityId),
        },
      }),
    [navigate, communityId],
  )

  return (
    <>
      <Typography variant="subtitle1" color="textSecondary">
        Owners
      </Typography>
      <Box>
        <FormControlLabel
          control={
            <Checkbox checked={!!search.hideOthers} onChange={onChange} />
          }
          label="Show only selected owners items"
        />
      </Box>
      <Box className={styles.infoUsers}>
        <UserChips
          tagList={users}
          onViewUser={onViewUser}
          onOpenUserProfile={onOpenUserProfile}
        />
      </Box>
    </>
  )
}
