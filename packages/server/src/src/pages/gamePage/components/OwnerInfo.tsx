import { Box, Grid2, Typography } from "@mui/material"
import dayjs from "dayjs"
import localizedFormat from "dayjs/plugin/localizedFormat"
import { useMemo } from "react"

import { gameRoute } from "../../../routes/game.route"
import { IGameGameUser } from "../../../types/tRPC.types"
import * as styles from "../gamePage.css"

import { InfoRow } from "./InfoRow"

interface OwnerInfoProps {
  users: IGameGameUser[]
  id?: number
}
export const OwnerInfo = ({ users, id }: OwnerInfoProps) => {
  const search = gameRoute.useSearch()

  dayjs.extend(localizedFormat)

  const userSelected = useMemo(() => {
    const useId = search.userId ?? id
    const result = useId ? users?.find((user) => user.id === useId) : null
    return result ?? null
  }, [search, users, id])

  return (
    <>
      <Typography variant="subtitle1" color="textSecondary">
        {"Owners info"}
      </Typography>
      <Box className={styles.infoRowCell}>
        {userSelected !== null && (
          <Grid2 container spacing={2} columns={2}>
            <InfoRow title="" value={userSelected.name} />
            {(userSelected.rating ?? 0) > 0 && (
              <InfoRow title="Rating" value={String(userSelected.rating)} />
            )}
            {userSelected.lastPlay && (
              <InfoRow
                title="Last Play"
                value={dayjs(userSelected.lastPlay).format("ll")}
              />
            )}
            {(userSelected.playCount ?? 0) > 0 && (
              <InfoRow
                title="Play count"
                value={String(userSelected.playCount ?? 0)}
              />
            )}
            <InfoRow
              title="Can teach"
              value={userSelected.willTeach ? "Yes" : "No"}
            />
            {userSelected.portability && (
              <InfoRow title="Portability" value={userSelected.portability} />
            )}
            {userSelected.events && (
              <InfoRow title="Events" value={userSelected.events} />
            )}
          </Grid2>
        )}
      </Box>
    </>
  )
}
