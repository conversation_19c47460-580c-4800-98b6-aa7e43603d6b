import classnames from "classnames"
import dayjs from "dayjs"
import { useMemo } from "react"

import { GameThumbnail } from "../../../components/GameThumbnail/GameThumbnail"
import { ICAvatarUser } from "../../../components/UserAvatar/UserAvatar"
import { LAST_UPDATE_PERIOD } from "../../../config/game.conf"
import { gameRoute } from "../../../routes/game.route"
import { IGameViewExpansion } from "../../../types/tRPC.types"

import * as styles from "./expansionThumbnail.css"

interface ExpansionThumbnailProps {
  expansion: IGameViewExpansion
  onViewUser: (user?: number) => void
  users: ICAvatarUser[]
}
export const GameThumbnailWrapper = ({
  expansion,
  onViewUser,
  users,
}: ExpansionThumbnailProps) => {
  const { userId } = gameRoute.useSearch()

  const hasOwner = useMemo(() => {
    let hasOwnerL = !userId
    expansion.users?.forEach((user) => {
      if (user === userId) {
        hasOwnerL = true
      }
    })

    return hasOwnerL
  }, [expansion.users, userId])

  const userList = useMemo(() => {
    return expansion.users
      .map((user: number) => {
        return users.find((gUser) => gUser.id === user)
      })
      .filter((user) => user !== undefined)
  }, [expansion.users, users])

  const isNew = !dayjs(expansion.news).isBefore(dayjs(), LAST_UPDATE_PERIOD)

  return (
    <GameThumbnail
      userList={userList}
      className={classnames(styles.card, {
        [styles.hasOwner]: hasOwner,
        [styles.base]:
          expansion.type === "base" || expansion.type === "base-expansion",
      })}
      onUser={onViewUser}
      game={expansion}
      isNew={isNew}
    />
  )
}
