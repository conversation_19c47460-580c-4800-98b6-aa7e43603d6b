import { Box, Button, Grid2, Typo<PERSON> } from "@mui/material"
import {
  useNavigate,
  useParentMatches,
  useRouter,
} from "@tanstack/react-router"
import { useCallback, useEffect } from "react"

import { BggLink } from "../../components/BggLink/BggLink"
import { TitleRow } from "../../components/titleRow/TitleRow"
import { GAME_IMAGES } from "../../config/images"
import { createRoutes } from "../../routes/breadcrumbs"
import { gameRoute } from "../../routes/game.route"
import { COMMUNITIES_ROOT_ROUTE, GAME_ROUTE } from "../../routes/paths"
import { useBreadcrumbsStore } from "../../store/useBreadcrumbsStore"
import { useIsMobileStore } from "../../store/useIsMobileStore"
import { useUserStore } from "../../store/useUserStore"

import { ChipList } from "./components/ChipList"
import { ExpansionList } from "./components/ExpansionList"
import { GameInfo } from "./components/GameInfo"
import { OwnerInfo } from "./components/OwnerInfo"
import { SelectOwner } from "./components/SelectOwner"
import * as styles from "./gamePage.css"

export const GamePage = () => {
  const base = useParentMatches().find(
    (match) => match.fullPath === COMMUNITIES_ROOT_ROUTE,
  )?.loaderData
  const game = gameRoute.useLoaderData()
  const search = gameRoute.useSearch()
  const params = gameRoute.useParams()
  const myData = useUserStore((state) => state.userData)
  const setBreadcrumbs = useBreadcrumbsStore((state) => state.setBreadcrumbs)
  const navigate = useNavigate()
  const sizeThresholdList = useIsMobileStore((state) => state.sizeThresholdList)

  const router = useRouter()

  if (!game) return null

  const back = () => router.history.back()

  useEffect(() => {
    createRoutes(
      setBreadcrumbs,
      "gameRoute",
      {
        indexRoute: {},
        communitiesRoute: {},
        communityRoute: { communityId: params.communityId },
        gamesRoute: {},
        gameRoute: {},
      },
      {
        $community: base?.name ?? "",
        $game: game.game.title,
      },
    )
  }, [params.communityId, game.game.title, base?.name])

  const onViewUser = useCallback(
    (id?: number) =>
      navigate({
        to: GAME_ROUTE,
        params: {
          communityId: params.communityId,
          gameId: params.gameId,
        },
        search: {
          userId: id,
          hideOthers: search.hideOthers,
        },
      }),
    [navigate, params, search],
  )

  const isLargeTablet = sizeThresholdList.largeTablet

  const isMobile = sizeThresholdList.mobile

  return (
    <>
      <TitleRow title={game.game.title}>
        <Button variant="outlined" onClick={back}>
          Back
        </Button>
      </TitleRow>
      <Box padding={2} pl={isLargeTablet ? 0 : 4}>
        <Box pt={2}>
          <Grid2 container columns={10} spacing={2}>
            <Grid2
              sx={{ width: "250px" }}
              size={isLargeTablet ? (isMobile ? 10 : 5) : 2}
            >
              <Box className={styles.imageBox}>
                <img
                  src={`${ENV_IMAGE_CDN}${GAME_IMAGES}/${game.game.id}.jpg`}
                />
                <Box className={styles.link}>
                  <BggLink bggId={game.game.bggId} />
                  {game.game.average && (
                    <Box className={styles.bggRating}>
                      <Typography fontWeight={600}>
                        {Math.round(game.game.average * 10) / 10}
                      </Typography>
                    </Box>
                  )}
                </Box>
              </Box>
            </Grid2>
            <Grid2
              size={isLargeTablet ? (sizeThresholdList.tablet ? 10 : 5) : 2}
            >
              <GameInfo game={game.game} />
            </Grid2>
            {isLargeTablet && (
              <Grid2 size={10}>
                <Typography variant="subtitle1" color="textSecondary">
                  Tags
                </Typography>
                <Box className={styles.infoRowCell}>
                  <ChipList tagList={game.tags} />
                </Box>
              </Grid2>
            )}
            <Grid2 size={isLargeTablet ? 5 : 2}>
              <SelectOwner
                communityId={base?.id ?? 0}
                search={search}
                users={game.game.users}
                onViewUser={onViewUser}
              />
            </Grid2>
            {search.userId ? (
              <Grid2 size={isLargeTablet ? 5 : 2}>
                <OwnerInfo users={game.game.users} id={myData.id} />
              </Grid2>
            ) : (
              <Grid2 size={2}>
                <Typography color="textSecondary">
                  Select owner to see more data
                </Typography>
              </Grid2>
            )}
          </Grid2>
          {!isLargeTablet && (
            <Grid2 size={10}>
              <Typography variant="subtitle1" color="textSecondary">
                Tags
              </Typography>
              <Box className={styles.infoRowCell}>
                <ChipList tagList={game.tags} />
              </Box>
            </Grid2>
          )}
        </Box>
        {game.expansions.length > 0 && (
          <Box pt={2}>
            <ExpansionList
              users={game.game.users}
              expansions={game.expansions}
              onViewUser={onViewUser}
            />
          </Box>
        )}
      </Box>
    </>
  )
}
