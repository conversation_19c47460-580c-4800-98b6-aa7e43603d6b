import { useEffect } from "react"

import { CommunityList } from "../../components/communityList/CommunityList"
import { createRoutes } from "../../routes/breadcrumbs"
import { publicCommunitiesRoute } from "../../routes/community.route"
import { useBreadcrumbsStore } from "../../store/useBreadcrumbsStore"
import { RouterOutput } from "../../trpc/trpc"

type CommunityListData = RouterOutput["communityList"]

export const PublicCommunitiesPage = () => {
  const communities: CommunityListData | null =
    publicCommunitiesRoute.useLoaderData()
  const setBreadcrumbs = useBreadcrumbsStore((state) => state.setBreadcrumbs)

  useEffect(() => {
    createRoutes(setBreadcrumbs, "publicCommunitiesRoute", {
      indexRoute: {},
      publicCommunitiesRoute: {},
    })
  }, [])

  if (!communities) {
    return <></>
  }

  //return null
  return <CommunityList title="Public Communities" communities={communities} />
}
