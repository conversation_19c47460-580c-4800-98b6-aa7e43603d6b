import { Box } from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import { useCallback, useMemo } from "react"

import {
  GameSearch,
  SearchParams,
} from "../../../components/GameSearch/GameSearch"
import {
  ICTag,
  ICTagCat,
} from "../../../components/GameSearch/components/ChipList"
import { ICSearchBoxTag } from "../../../components/GameSearch/components/SearchBox"
import { GamesList } from "../../../components/gamesList/GamesList"
import { PROFILE_GAME_ROUTE, PROFILE_ROUTE } from "../../../routes/paths"
import { usersProfileRoute } from "../../../routes/usersProfile.route"
import { IUserProfileGame } from "../../../types/tRPC.types"
import { applyFilters } from "../../../utils/filter"

interface UserExpansionsProps {
  games: IUserProfileGame[]
  tags?: ICSearchBoxTag[]
  tagCategories?: ICTagCat[]
}

export const UserGames = ({
  games,
  tags,
  tagCategories,
}: UserExpansionsProps) => {
  const search = usersProfileRoute.useSearch()
  const params = usersProfileRoute.useParams()
  const navigate = useNavigate()

  const onChange = useCallback(
    (page: number) => {
      navigate({
        to: PROFILE_ROUTE,
        params,
        search: {
          ...search,
          page,
          tab: "games",
        },
      })

      window.scrollTo({ top: 0 })
    },
    [navigate, params, search],
  )

  const onNavigate = useCallback(
    (search: SearchParams) =>
      navigate({
        to: PROFILE_ROUTE,
        params,
        search: {
          ...search,
          tab: "games",
        },
      }),
    [navigate, params],
  )

  const optimizeTags = useMemo(() => {
    const populatedTags: ICTag[][] = []

    games.forEach((currentGame) => {
      populatedTags[currentGame.id] = currentGame.tags!.map(
        (currentTag) => (tags ?? []).find((tag) => tag.id === currentTag)!,
      )
    })

    return populatedTags
  }, [tags])

  const useGameList = useMemo(() => {
    return applyFilters(games, search, optimizeTags)
  }, [games, search])

  return (
    <Box
      display="flex"
      gap={4}
      flexWrap="wrap"
      justifyContent="center"
      alignItems="center"
      flexDirection="column"
      width="100%"
      padding={2}
      boxSizing="border-box"
    >
      <GameSearch
        search={search}
        onNavigate={onNavigate}
        personalOrder
        tags={tags}
        tagCategories={tagCategories}
      />
      <GamesList
        games={useGameList}
        navigation={{
          to: PROFILE_GAME_ROUTE,
          params: {},
        }}
        onPageChange={onChange}
        page={search.page ?? 1}
      />
    </Box>
  )
}
