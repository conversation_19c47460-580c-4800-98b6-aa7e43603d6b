import CasinoIcon from "@mui/icons-material/Casino"
import PermIdentityIcon from "@mui/icons-material/PermIdentity"
import { Tab, Tabs } from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import { SyntheticEvent, useCallback } from "react"

import { PROFILE_ROUTE } from "../../../routes/paths"
import { usersProfileRoute } from "../../../routes/usersProfile.route"

export const MenuBar = () => {
  const search = usersProfileRoute.useSearch()
  const navigate = useNavigate()
  const handleChange = useCallback(
    (_: SyntheticEvent<Element, Event>, value: string) => {
      navigate({
        to: PROFILE_ROUTE,
        params: {},
        search: {
          tab: value,
        },
      })
    },
    [navigate],
  )

  return (
    <Tabs value={search.tab ?? "profile"} onChange={handleChange}>
      <Tab value="profile" icon={<PermIdentityIcon />} label="My data" />
      <Tab value="games" icon={<CasinoIcon />} label="Games" />
    </Tabs>
  )
}
