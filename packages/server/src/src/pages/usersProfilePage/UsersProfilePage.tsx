import { useEffect } from "react"

import { createRoutes } from "../../routes/breadcrumbs"
import { usersProfileRoute } from "../../routes/usersProfile.route"
import { useBreadcrumbsStore } from "../../store/useBreadcrumbsStore"

import { MenuBar } from "./components/MenuBar"
import { GamesSubpage } from "./subpages/GamesSubpage"
import { ProfileSubpage } from "./subpages/ProfileSubpage"

export const UsersProfilePage = () => {
  const search = usersProfileRoute.useSearch()
  const setBreadcrumbs = useBreadcrumbsStore((state) => state.setBreadcrumbs)

  useEffect(() => {
    createRoutes(
      setBreadcrumbs,
      "profileRoute",
      {
        indexRoute: {},
        profileRoute: {},
      },
      {},
    )
  }, [])

  return (
    <div>
      <MenuBar />
      {(!search.tab || search.tab === "profile") && <ProfileSubpage />}
      {search.tab === "games" && <GamesSubpage />}
    </div>
  )
}
