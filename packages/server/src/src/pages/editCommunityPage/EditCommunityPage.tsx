import { useParentMatches, useRouter } from "@tanstack/react-router"
import React, { useEffect } from "react"

import { CommunityConfiguration } from "../../components/CommunityConfiguration/CommunityConfiguration"
import { CommunityUploadImage } from "../../components/CommunityUploadImage/CommunityUploadImage"
import { hasPermission } from "../../permissions"
import { COMMUNITIES_ROOT_ROUTE } from "../../routes/paths"
import { useUserStore } from "../../store/useUserStore"

export const EditCommunityPage = () => {
  const [saved, setSaved] = React.useState(false)
  const router = useRouter()
  const [savedImage, setSavedImage] = React.useState<number>(0)
  const base = useParentMatches().find(
    (match) => match.fullPath === COMMUNITIES_ROOT_ROUTE,
  )?.loaderData

  useEffect(() => {
    if (savedImage > 0) {
      router.invalidate()
    }
  }, [savedImage, router])

  const user = useUserStore((state) => state.userData)
  if (!hasPermission(user, "community", "update", { id: base?.id ?? 0 })) {
    return null
  }

  return (
    base?.id && (
      <>
        <CommunityUploadImage
          id={base.id}
          onSuccess={() => setSavedImage(savedImage + 1)}
          hasImage={base.image}
          notNew={true}
        />
        <CommunityConfiguration edit={base} onSuccess={() => setSaved(true)} />
      </>
    )
  )
}
