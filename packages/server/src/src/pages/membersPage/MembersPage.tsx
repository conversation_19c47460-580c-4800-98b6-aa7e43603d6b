import { Box } from "@mui/material"
import { useNavigate, useParentMatches } from "@tanstack/react-router"
import { useCallback, useEffect } from "react"

import { UserRow } from "../../components/UserRow/UserRow"
import { TitleRow } from "../../components/titleRow/TitleRow"
import { createRoutes } from "../../routes/breadcrumbs"
import { membersRoute } from "../../routes/members.route"
import {
  COMMUNITIES_ROOT_ROUTE,
  COMMUNITY_USER_ROUTE,
} from "../../routes/paths"
import { useBreadcrumbsStore } from "../../store/useBreadcrumbsStore"

export const MembersPage = () => {
  const base = useParentMatches().find(
    (match) => match.fullPath === COMMUNITIES_ROOT_ROUTE,
  )?.loaderData
  const users = membersRoute.useLoaderData()
  const navigate = useNavigate()
  const setBreadcrumbs = useBreadcrumbsStore((state) => state.setBreadcrumbs)

  useEffect(() => {
    createRoutes(
      setBreadcrumbs,
      "usersRoute",
      {
        indexRoute: {},
        communitiesRoute: {},
        communityRoute: { communityId: String(base?.id ?? 0) },
        usersRoute: {},
      },
      {
        $community: base?.name ?? "",
      },
    )
  }, [base])

  if (!users) return null

  const onClick = useCallback(
    (id?: number) =>
      navigate({
        to: COMMUNITY_USER_ROUTE,
        params: {
          communityId: String(base?.id ?? 0),
          userId: String(id),
        },
      }),
    [navigate, base?.id],
  )

  return (
    <>
      <TitleRow title="Members" />
      <Box
        alignContent="center"
        justifyContent="center"
        display="flex"
        flexDirection="row"
        gap={4}
        flexWrap="wrap"
        padding={5}
      >
        {users
          .sort((u1, u2) => (u1.name > u2.name ? 1 : -1))
          .map((user) => (
            <UserRow
              key={user.id}
              user={user}
              communityId={String(base?.id ?? 0)}
              onClick={onClick}
            />
          ))}
      </Box>
    </>
  )
}
