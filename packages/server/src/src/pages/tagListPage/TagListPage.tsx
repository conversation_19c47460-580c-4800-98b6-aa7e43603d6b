import { Box } from "@mui/material"
import { useEffect } from "react"

import { createRoutes } from "../../routes/breadcrumbs"
import { tagListRoute } from "../../routes/tagList.route"
import { useBreadcrumbsStore } from "../../store/useBreadcrumbsStore"

import { ChipList } from "./components/ChipList"
import * as styles from "./tagListPage.css"

export const TagListPage = () => {
  const response = tagListRoute.useLoaderData()
  const setBreadcrumbs = useBreadcrumbsStore((state) => state.setBreadcrumbs)

  useEffect(() => {
    createRoutes(
      setBreadcrumbs,
      "tagListRoute",
      {
        indexRoute: {},
        tagListRoute: {},
      },
      {},
    )
  }, [])

  if (!response) return null

  const { tags, tagCategories } = response

  return (
    <Box className={styles.container}>
      <ChipList tagList={tags} tagCategories={tagCategories} />
    </Box>
  )
}
