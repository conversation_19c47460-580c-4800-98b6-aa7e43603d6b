@value isTablet from "../../css/size.css";


.gridTitle {
  display:flex;
  flex-direction:row;
  width: 100%;
  justify-content: flex-start;
  margin-bottom: var(--spacing-2);
}

.gridTitleRight {
  composes: gridTitle;
}

@media (min-width: isTablet) {
  .gridTitleRight {
    justify-content: flex-end;
  }
}

.tabWrapper {
  margin-top: var(--spacing-6);
}

.contentWrapper {
  padding: var(--spacing-4) 0;
}

.welcome {
  display:flex;
  flex-direction:row;
  width: 100%;
  justify-content: center;
  align-items: center;
}