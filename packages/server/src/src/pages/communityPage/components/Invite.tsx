import { But<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>Content, DialogTitle } from "@mui/material"
import { useState } from "react"

import { LinkInvite } from "./LinkInvite"

interface InviteProps {
  communityId: number
}
export const Invite = ({ communityId }: InviteProps) => {
  const [visible, setVisible] = useState(false)
  const [openTab, setOpenTab] = useState(0)

  const handleClose = () => setVisible(false)
  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setOpenTab(newValue)
  }

  return (
    <>
      <Button onClick={() => setVisible(true)} variant="outlined">
        Create Invite
      </Button>
      <Dialog open={visible} onClose={handleClose}>
        <DialogTitle>Create Invite</DialogTitle>
        <DialogContent>
          <LinkInvite communityId={communityId} onClose={handleClose} />
        </DialogContent>
      </Dialog>
    </>
  )
}

/*

  <Tabs
    value={openTab}
    onChange={handleChange}
  >
    <Tab label="Link invite" value={0} />
    <Tab label="User invite" value={1} />
  </Tabs>
  {openTab === 0 && (
    <LinkInvite communityId={communityId} onClose={handleClose} />
  )}
  {openTab === 1 && <>T2</>}
          
 */
