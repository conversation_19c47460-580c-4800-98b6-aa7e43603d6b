
.title {
    height: 145px;
    padding-right: var(--spacing-2);
}

.name {
    text-overflow: ellipsis;
    text-wrap-mode: nowrap;
    overflow: hidden;
}

.nameWrapper {
    margin-left: var(--spacing-2);
    padding-bottom: var(--spacing-2);
}

.titleWrapper {
    min-width: 350px;
}

.location {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-h);
}

.info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
    padding: var(--spacing-2);
    align-items: flex-start;
}

.buttonWrapper {
    display: flex;
    flex-direction: row;
    gap: var(--spacing-2);
    justify-content: flex-start;
    align-items: center;
}

.titleBox {
    margin-top: var(--spacing-1);
    display: flex;
    justify-content: space-between;
}

.leftTitleBox {
    display: flex;
    gap: var(--spacing-4);
    margin-right: var(--spacing-4);
}


.membershipStatus {
    display: flex;
    justify-content: flex-end;
    padding-top: var(--spacing-2);
}