import {
  Button,
  <PERSON>alog,
  <PERSON>alogA<PERSON>,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  Switch,
} from "@mui/material"
import { ChangeEvent } from "react"

interface AllowShareProps {
  open: boolean
  onClose: () => void
  onContinue: () => void
  onChange: (value: boolean) => void
}
export const AllowShare = ({
  onClose,
  onContinue,
  onChange,
  open,
}: AllowShareProps) => (
  <Dialog open={open} onClose={onClose}>
    <DialogTitle>Join community</DialogTitle>
    <DialogContent>
      <FormControlLabel
        control={
          <Switch
            onChange={(event: ChangeEvent<HTMLInputElement>, value) =>
              onChange(value)
            }
          />
        }
        label="Share my collection"
      />
    </DialogContent>
    <DialogActions>
      <Button onClick={onClose}>Cancel</Button>
      <Button onClick={onContinue} autoFocus>
        Continue
      </Button>
    </DialogActions>
  </Dialog>
)
