import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { <PERSON>adingButton } from "@mui/lab"
import { <PERSON><PERSON>, AlertTitle, Box, Button, Grid2 } from "@mui/material"
import dayjs from "dayjs"
import React from "react"
import { <PERSON><PERSON><PERSON><PERSON>, type SubmitHandler, useForm } from "react-hook-form"

import {
  CreateInviteInputs,
  createInviteInputs,
} from "../../../../../../common/src/zodSchemas/postSchemas/createInviteInputs"
import { FormDate } from "../../../components/elements/HookElements/FormDate"
import { FormInput } from "../../../components/elements/HookElements/FormInput"
import { trpc } from "../../../trpc/trpc"

interface LinkInviteProps {
  communityId: number
  onClose: () => void
}

const INVITE_IS_VALID_DAYS = 7

export const LinkInvite = ({ communityId, onClose }: LinkInviteProps) => {
  const [saving, setSaving] = React.useState(false)
  const [success, setSuccess] = React.useState<string | null>(null)
  const [copied, setCopied] = React.useState(false)
  const methods = useForm<CreateInviteInputs>({
    resolver: zodResolver(createInviteInputs),
    defaultValues: {
      count: 1,
      expiration: dayjs(new Date())
        .add(INVITE_IS_VALID_DAYS, "day")
        .format("YYYY-MM-DD"),
    },
  })

  const onSubmit: SubmitHandler<CreateInviteInputs> = (data) => {
    setSaving(true)
    trpc.createInvite
      .mutate({
        ...data,
        communityId,
      })
      .then((response) => {
        if (response.success) {
          setSuccess(response.link)
        }
      })
      .finally(() => {
        setSaving(false)
      })
  }

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(success ?? "")
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error("Failed to copy text: ", err)
    }
  }

  return !success ? (
    <Box>
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit)}>
          <Grid2 container columns={2} spacing={2} marginTop={2}>
            <Grid2 size={2}>
              <FormInput
                label="How many people can join?"
                name="count"
                numeric
              />
            </Grid2>
            <Grid2 size={2}>
              <FormDate
                minDate={new Date()}
                label="When does invite expire?"
                name="expiration"
              />
            </Grid2>
            <Grid2 size={1}>
              <LoadingButton
                loading={saving}
                variant="contained"
                color="primary"
                type="submit"
              >
                Create
              </LoadingButton>
            </Grid2>
            <Grid2 size={1}>
              <Button variant="contained" color="secondary" onClick={onClose}>
                Close
              </Button>
            </Grid2>
          </Grid2>
        </form>
      </FormProvider>
    </Box>
  ) : (
    <Box>
      <Alert onClick={handleCopyLink} severity={copied ? "success" : "info"}>
        <AlertTitle>Invite link:</AlertTitle>
        {success} <br />
        {copied && "Copied to clipboard"}
      </Alert>
    </Box>
  )
}
