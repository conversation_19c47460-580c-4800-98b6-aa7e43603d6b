@value isTablet from "../../../css/size.css";

.name {
  text-overflow: ellipsis;
  text-wrap-mode: nowrap;
  overflow: hidden;
  width: 100%;
}

.nameWrapper {
}

.titleWrapper {
  width: 100%;
}

.location {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-h);
}

.info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
  padding: var(--spacing-2) 0;
  align-items: flex-start;
}

.buttonWrapper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  justify-content: flex-start;
  align-items: center;
}

.titleBox {
  margin-top: var(--spacing-1);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.leftTitleBox {
  display: flex;
  gap: var(--spacing-4);
  margin-right: var(--spacing-4);
}

.contentWrapper {
  padding: var(--spacing-4) 0;
}

.membershipStatus {
  display: flex;
  justify-content: flex-start;
  padding-top: var(--spacing-2);
}