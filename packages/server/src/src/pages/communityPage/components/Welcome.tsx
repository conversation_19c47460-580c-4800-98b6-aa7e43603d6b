import { Box } from "@mui/material"
import { useParentMatches } from "@tanstack/react-router"

import { COMMUNITIES_ROOT_ROUTE } from "../../../routes/paths"

export const Welcome = () => {
  const base = useParentMatches().find(
    (match) => match.fullPath === COMMUNITIES_ROOT_ROUTE,
  )?.loaderData

  return (
    <Box width="100%" marginTop={2}>
      <div dangerouslySetInnerHTML={{ __html: base?.welcome ?? "" }} />
    </Box>
  )
}
