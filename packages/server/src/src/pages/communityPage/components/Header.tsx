import CardMembershipIcon from "@mui/icons-material/CardMembership"
import HourglassEmptyIcon from "@mui/icons-material/HourglassEmpty"
import { LoadingButton } from "@mui/lab"
import { Alert, Box, Grid2, Paper, Typography } from "@mui/material"
import { useParentMatches } from "@tanstack/react-router"
import React, { useCallback, useState } from "react"

import { hasPermission } from "../../../../../../common/src/permissions/hasPermissions"
import { hasCommunityRole } from "../../../../../../common/src/permissions/roles/helpers/hasCommunityRole"
import { LoaderDialog } from "../../../components/LoaderDialog/LoaderDialog"
import { PartyLink } from "../../../components/elements/link/PartyLink/PartyLink"
import { communityOpenRoute } from "../../../routes/community.route"
import {
  COMMUNITIES_ROOT_ROUTE,
  COMMUNITY_EDIT_ROUTE,
  COMMUNITY_USERS_ROUTE,
  COMMUNITY_USER_ROUTE,
  GAMES_ROUTE,
} from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"
import { trpc } from "../../../trpc/trpc"

import { AllowShare } from "./AllowShare"
import { LeftInfoBox } from "./LeftInfoBox"
import * as styles from "./header.css"

export const Header = () => {
  const community = communityOpenRoute.useLoaderData()
  const base = useParentMatches().find(
    (match) => match.fullPath === COMMUNITIES_ROOT_ROUTE,
  )?.loaderData

  const [sendingJoin, setSendingJoin] = useState<boolean>(false)
  const [receivedJoinSent, setReceivedJoinSent] = useState<boolean>(false)
  const [doShare, setDoShare] = useState<boolean>(false)
  const [failedJoin, setFailedJoin] = useState<boolean>(false)
  const [requestShare, setRequestShare] = useState<boolean>(false)

  if (!base || !community) {
    return null
  }

  const onJoinDo = useCallback(async () => {
    setRequestShare(false)
    setSendingJoin(true)
    trpc.joinCommunity
      .mutate({
        communityId: base?.id ?? 0,
        shareMyGames: doShare,
      })
      .then((response) => {
        if (response === "OK") {
          setReceivedJoinSent(true)
        }
        setSendingJoin(false)
      })
      .catch((error) => {
        setFailedJoin(true)
        setSendingJoin(false)
      })
  }, [
    trpc,
    base?.id,
    doShare,
    setRequestShare,
    setReceivedJoinSent,
    setSendingJoin,
    setFailedJoin,
  ])

  const onJoin = useCallback(() => {
    if (!base?.share) {
      setRequestShare(true)
    } else {
      onJoinDo()
    }
  }, [trpc, base?.id, requestShare])

  if (!community) {
    return <></>
  }

  const userInfo = useUserStore((state) => state.userData)

  const isMember = hasPermission(userInfo, "community", "isMember", {
    id: base.id,
  })

  const isInvited = hasCommunityRole(userInfo.roles ?? [], "invited", base.id)

  return (
    <>
      <Box className={styles.titleBox}>
        <Box className={styles.leftTitleBox}>
          <LeftInfoBox
            base={base}
            canInvite={hasPermission(userInfo, "community", "invite", base)}
          />
          <Box className={styles.buttonWrapper}>
            <Box>
              <PartyLink
                variant="outlined"
                to={GAMES_ROUTE}
                params={{ communityId: String(base.id) }}
              >
                Games
              </PartyLink>
            </Box>
            <Box>
              <PartyLink
                to={COMMUNITY_USERS_ROUTE}
                params={{ communityId: String(base.id) }}
                variant="outlined"
              >
                Members
              </PartyLink>
            </Box>
            <Box>
              <PartyLink
                to={COMMUNITY_USER_ROUTE}
                params={{
                  communityId: String(base.id),
                  userId: String(userInfo?.id ?? 0),
                }}
                variant="outlined"
              >
                Profile
              </PartyLink>
            </Box>
            {hasPermission(userInfo, "community", "update", base) && (
              <Box>
                <PartyLink
                  to={COMMUNITY_EDIT_ROUTE}
                  params={{ communityId: String(base.id) }}
                  variant="outlined"
                >
                  Configuration
                </PartyLink>
              </Box>
            )}
          </Box>
        </Box>
        <Box className={styles.titleWrapper}>
          <Paper elevation={2} className={styles.title}>
            <Grid2 container columns={4} spacing={1}>
              <Grid2 size={2}>
                <Box margin={2}>
                  <Typography variant="body2" fontWeight="bold">
                    {base.openness.toUpperCase()}
                  </Typography>
                </Box>
              </Grid2>
              <Grid2 size={2}>
                {isMember && (
                  <Box className={styles.membershipStatus}>
                    <Alert
                      icon={
                        <CardMembershipIcon color="info" fontSize="medium" />
                      }
                      severity="info"
                    >
                      Member
                    </Alert>
                  </Box>
                )}
                {!receivedJoinSent &&
                  !isMember &&
                  !isInvited &&
                  base.openness === "public" && (
                    <Box className={styles.membershipStatus}>
                      <LoadingButton
                        loadingPosition="start"
                        loading={sendingJoin}
                        variant="contained"
                        onClick={onJoin}
                      >{`${base.approval ? "Request to" : ""} Join`}</LoadingButton>
                    </Box>
                  )}
                {((receivedJoinSent && base.approval) || isInvited) && (
                  <Box className={styles.membershipStatus}>
                    <Alert
                      icon={<HourglassEmptyIcon fontSize="inherit" />}
                      severity="success"
                    >
                      Joining: Waiting for approve
                    </Alert>
                  </Box>
                )}
              </Grid2>
            </Grid2>
            <Box className={styles.nameWrapper}>
              <Typography variant="h6" color="textSecondary">
                Welcome to&nbsp;
              </Typography>
              <Typography fontWeight="500" variant="h4" className={styles.name}>
                {base.name}
              </Typography>
            </Box>
          </Paper>
        </Box>
      </Box>
      <AllowShare
        open={requestShare}
        onChange={setDoShare}
        onContinue={onJoinDo}
        onClose={() => setRequestShare(false)}
      />
      <LoaderDialog
        state={failedJoin ? "failed" : null}
        title="Failed to send Join request"
        onClose={() => {
          setFailedJoin(false)
        }}
      />
    </>
  )
}
