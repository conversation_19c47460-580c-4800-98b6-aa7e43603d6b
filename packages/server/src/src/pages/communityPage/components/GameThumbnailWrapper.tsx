import dayjs from "dayjs"
import { useMemo } from "react"

import {
  GameThumbnail,
  ICNavigationProps,
  ICThumbnailGame,
} from "../../../components/GameThumbnail/GameThumbnail"
import {
  ICAvatarUser,
  ICFAvatarOnClick,
} from "../../../components/UserAvatar/UserAvatar"
import { LAST_UPDATE_PERIOD } from "../../../config/game.conf"

interface GameThumbnailProps {
  game: ICThumbnailGame & {
    news: string | null
    users: [number, number][]
    average: number
  }
  communityId: number
  onClick: ICFAvatarOnClick
  lookupUsers: ICAvatarUser[]
  navigation?: ICNavigationProps
}
export const GameThumbnailWrapper = ({
  game,
  lookupUsers,
  communityId,
  onClick,
  navigation,
}: GameThumbnailProps) => {
  const isNew = !dayjs(game.news).isBefore(dayjs(), LAST_UPDATE_PERIOD)

  const userList = useMemo(() => {
    return game.users
      .map((user) => lookupUsers.find((look) => look.id == user[0]))
      .filter((user) => user !== undefined)
  }, [game.users, lookupUsers])

  return (
    <GameThumbnail
      navigation={navigation}
      game={game}
      communityId={communityId}
      onUser={onClick}
      userList={userList}
      isNew={isNew}
    />
  )
}
