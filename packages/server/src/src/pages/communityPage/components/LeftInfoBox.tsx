import LanguageIcon from "@mui/icons-material/Language"
import MapIcon from "@mui/icons-material/Map"
import { Box, Link, Paper, Typography } from "@mui/material"
import React from "react"

import { Invite } from "./Invite"
import * as styles from "./header.css"

interface LeftInfoBoxProps {
  base: {
    location?: string | null
    online?: string | null
    id: number
  }
  canInvite: boolean
}
export const LeftInfoBox = ({ base, canInvite }: LeftInfoBoxProps) => {
  return (
    <Paper elevation={2} className={styles.title}>
      <Box className={styles.info}>
        {base.location && (
          <Box className={styles.location} title={base.location}>
            <MapIcon />
            <Typography variant="body1">{base.location}</Typography>{" "}
          </Box>
        )}
        {base.online && (
          <Box className={styles.location} title={base.online}>
            <LanguageIcon />
            <Link
              href={base.online}
              underline="none"
              variant="body1"
              target="_blank"
            >
              Open link
            </Link>{" "}
          </Box>
        )}
        {canInvite && <Box>{<Invite communityId={base.id} />}</Box>}
      </Box>
    </Paper>
  )
}
