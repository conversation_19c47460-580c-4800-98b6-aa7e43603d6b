import { <PERSON>, Grid2, Tab, Tabs, Typography } from "@mui/material"
import { useNavigate, useParentMatches } from "@tanstack/react-router"
import React, { useCallback, useEffect } from "react"

import { UserRow } from "../../components/UserRow/UserRow"
import { PartyLink } from "../../components/elements/link/PartyLink/PartyLink"
import { displayUsersCount } from "../../config/community.conf"
import { createRoutes } from "../../routes/breadcrumbs"
import { communityOpenRoute } from "../../routes/community.route"
import {
  COMMUNITIES_ROOT_ROUTE,
  COMMUNITY_ROUTE,
  COMMUNITY_USERS_ROUTE,
  COMMUNITY_USER_ROUTE,
  GAMES_ROUTE,
  GAME_ROUTE,
} from "../../routes/paths"
import { useBreadcrumbsStore } from "../../store/useBreadcrumbsStore"
import { useIsMobileStore } from "../../store/useIsMobileStore"

import * as styles from "./community.css"
import { GameThumbnailWrapper } from "./components/GameThumbnailWrapper"
import { Header } from "./components/Header"
import { HeaderMobile } from "./components/HeaderMobile"
import { Welcome } from "./components/Welcome"

export const CommunityPage = () => {
  const community = communityOpenRoute.useLoaderData()
  const search = communityOpenRoute.useSearch()
  const sizeThresholdList = useIsMobileStore((state) => state.sizeThresholdList)

  const base = useParentMatches().find(
    (match) => match.fullPath === COMMUNITIES_ROOT_ROUTE,
  )?.loaderData

  const navigate = useNavigate()
  const linkParams = communityOpenRoute.useParams()
  const setBreadcrumbs = useBreadcrumbsStore((state) => state.setBreadcrumbs)

  useEffect(() => {
    createRoutes(
      setBreadcrumbs,
      "communityRoute",
      {
        indexRoute: {},
        communitiesRoute: {},
        communityRoute: {},
      },
      {
        $community: base?.name ?? "",
      },
    )
  }, [base?.name])

  const onClick = useCallback(
    (id?: number) =>
      navigate({
        to: COMMUNITY_USER_ROUTE,
        params: {
          userId: String(id),
          communityId: String(base?.id ?? 0),
        },
      }),
    [navigate, base?.id],
  )

  const handleChange = useCallback(
    (event: React.SyntheticEvent, newValue: string) => {
      navigate({
        to: COMMUNITY_ROUTE,
        params: {
          communityId: String(base?.id ?? 0),
        },
        search: {
          tab: newValue,
        },
      })
    },
    [navigate, search, base?.id],
  )

  if (!base || !community) {
    return <Typography>Loading data</Typography>
  }

  const isLargeTablet = sizeThresholdList.largeTablet

  const isWelcome = (base?.welcome?.length ?? 0) > 7

  const defaultTab = isWelcome ? "welcome" : "info"

  return (
    <>
      {isLargeTablet && (
        <Box className={styles.tabWrapper}>
          <Tabs value={search.tab ?? defaultTab} onChange={handleChange}>
            {isWelcome && <Tab value="welcome" label="Welcome" />}
            <Tab value="info" label="Info" />
            <Tab value="games" label="New Games" />
            <Tab value="members" label="New Members" />
          </Tabs>
        </Box>
      )}
      {isLargeTablet && (search.tab ?? defaultTab) === "info" && (
        <HeaderMobile />
      )}
      {isLargeTablet &&
        isWelcome &&
        (search.tab ?? defaultTab) === "welcome" && <Welcome />}
      {!isLargeTablet && <Header />}
      {!isLargeTablet && isWelcome && (
        <Box className={styles.welcome}>
          <Welcome />
        </Box>
      )}
      <Box className={styles.contentWrapper}>
        <Grid2 container columns={12}>
          {(!isLargeTablet || search.tab === "games") && (
            <Grid2 size={isLargeTablet ? 12 : 9}>
              <Box className={styles.gridTitle}>
                <Typography variant="h5">New games</Typography>
                <PartyLink
                  to={GAMES_ROUTE}
                  params={{ communityId: linkParams.communityId }}
                >
                  See all games
                </PartyLink>
              </Box>
              <Box
                display="flex"
                gap={2}
                flexWrap="wrap"
                justifyContent="flex-start"
              >
                {community.games.games.map((game) => (
                  <GameThumbnailWrapper
                    navigation={{
                      to: GAME_ROUTE,
                      params: {
                        gameId: String(game.id),
                        communityId: String(linkParams.communityId),
                      },
                    }}
                    onClick={onClick}
                    lookupUsers={community.games.users}
                    game={game}
                    communityId={Number(linkParams.communityId)}
                    key={game.id}
                  />
                ))}
              </Box>
            </Grid2>
          )}
          {(!isLargeTablet || search.tab === "members") && (
            <Grid2 size={isLargeTablet ? 12 : 3}>
              <Box className={styles.gridTitleRight}>
                <Typography variant="h5">New members</Typography>
                <PartyLink
                  to={COMMUNITY_USERS_ROUTE}
                  params={{ communityId: linkParams.communityId }}
                >
                  See all members
                </PartyLink>
              </Box>
              <Box
                display="flex"
                gap={2}
                flexWrap="wrap"
                justifyContent={isLargeTablet ? "flex-start" : "flex-end"}
              >
                {community.users.slice(0, displayUsersCount).map((user) => (
                  <UserRow
                    onClick={onClick}
                    key={user.id}
                    user={user}
                    communityId={linkParams.communityId}
                  />
                ))}
              </Box>
            </Grid2>
          )}
        </Grid2>
      </Box>
    </>
  )
}
