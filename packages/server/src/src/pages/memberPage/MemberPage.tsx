import { Box, Typography } from "@mui/material"
import { useNavigate, useParentMatches } from "@tanstack/react-router"
import { useCallback, useEffect } from "react"

import {
  GameSearch,
  SearchParams,
} from "../../components/GameSearch/GameSearch"
import { PartyLink } from "../../components/elements/link/PartyLink/PartyLink"
import { TitleRow } from "../../components/titleRow/TitleRow"
import { hasPermission } from "../../permissions"
import { createRoutes } from "../../routes/breadcrumbs"
import { memberRoute } from "../../routes/member.route"
import {
  COMMUNITIES_ROOT_ROUTE,
  COMMUNITY_USERS_ROUTE,
  COMMUNITY_USER_ROUTE,
} from "../../routes/paths"
import { useBreadcrumbsStore } from "../../store/useBreadcrumbsStore"
import { useUserStore } from "../../store/useUserStore"

import { MenuBar } from "./components/MenuBar"
import { UserGames } from "./components/UserGames"
import { UserInfo } from "./components/UserInfo"
import * as styles from "./memberPage.css"

export const MemberPage = () => {
  const base = useParentMatches().find(
    (match) => match.fullPath === COMMUNITIES_ROOT_ROUTE,
  )?.loaderData
  const user = memberRoute.useLoaderData()
  const myData = useUserStore((store) => store.userData)
  const navigate = useNavigate()
  const search = memberRoute.useSearch()
  const params = memberRoute.useParams()

  const canApprove = hasPermission(myData, "community", "approve", {
    id: base?.id ?? 0,
  })

  const canShare = hasPermission(myData, "community", "shareUserGames", {
    id: base?.id ?? 0,
    viewedUserId: user?.id ?? 0,
    allowShare: base?.share ?? false,
  })

  if (!user) return null

  const setBreadcrumbs = useBreadcrumbsStore((state) => state.setBreadcrumbs)
  useEffect(() => {
    createRoutes(
      setBreadcrumbs,
      "userRoute",
      {
        indexRoute: {},
        communitiesRoute: {},
        communityRoute: { communityId: String(base?.id ?? 0) },
        usersRoute: {},
        userRoute: {},
      },
      {
        $community: base?.name ?? "",
        $user: user.name,
      },
    )
  }, [base?.name, base?.id, user.name])

  const onNavigate = useCallback(
    (search: SearchParams) =>
      navigate({
        to: COMMUNITY_USER_ROUTE,
        params,
        search: {
          ...search,
          tab: "games",
        },
      }),
    [navigate, params],
  )

  return (
    <>
      <MenuBar userId={user.id} communityId={base?.id ?? 0} />
      <Box className={styles.container}>
        <Box padding={4}>
          {(!search.tab || search.tab === "profile") && (
            <Box>
              <UserInfo
                canApprove={canApprove}
                user={user}
                canShare={canShare}
                communityId={base?.id ?? 0}
              />
            </Box>
          )}
        </Box>
        <TitleRow title={`Member: ${user.name}`}>
          <PartyLink
            variant="outlined"
            to={COMMUNITY_USERS_ROUTE}
            params={{ communityId: String(base?.id ?? 0) }}
          >
            All Members
          </PartyLink>
        </TitleRow>
      </Box>
      {search.tab === "games" && (
        <>
          {user.games ? (
            <Box
              display="flex"
              gap={4}
              flexWrap="wrap"
              justifyContent="center"
              alignItems="center"
              flexDirection="column"
              width="100%"
              padding={2}
              boxSizing="border-box"
            >
              <GameSearch
                onNavigate={onNavigate}
                search={search}
                personalOrder
                tags={user.tags}
                tagCategories={user.tagCategories}
              />
              <UserGames
                games={user.games}
                communityId={base?.id ?? 0}
                tags={user.tags}
              />
            </Box>
          ) : (
            <Box m={4}>
              <Typography>Games are not shared with this community</Typography>
            </Box>
          )}
        </>
      )}
    </>
  )
}
