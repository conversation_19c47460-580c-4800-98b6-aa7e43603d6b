import { useNavigate } from "@tanstack/react-router"
import { useCallback, useMemo } from "react"

import { ICTag } from "../../../components/GameSearch/components/ChipList"
import { ICSearchBoxTag } from "../../../components/GameSearch/components/SearchBox"
import { ICUserThumbnailWrapperGame } from "../../../components/gamesList/GameThumbnailWrapper"
import { GamesList } from "../../../components/gamesList/GamesList"
import { memberRoute } from "../../../routes/member.route"
import { COMMUNITY_USER_ROUTE, GAME_ROUTE } from "../../../routes/paths"
import { applyFilters } from "../../../utils/filter"

interface UserExpansionsProps {
  games: (ICUserThumbnailWrapperGame & {
    weight: number
    players: {
      box: { min: number; max: number }
      stats?: number[][] | undefined
    }
    tags: number[]
  })[]
  communityId: number
  tags?: ICSearchBoxTag[]
}

export const UserGames = ({
  games,
  communityId,
  tags,
}: UserExpansionsProps) => {
  const params = memberRoute.useParams()
  const search = memberRoute.useSearch()
  const navigate = useNavigate()

  const onChange = useCallback(
    (page: number) =>
      navigate({
        to: COMMUNITY_USER_ROUTE,
        params,
        search: {
          ...search,
          page,
          tab: "games",
        },
      }),
    [navigate, params, search],
  )

  const optimizeTags = useMemo(() => {
    const populatedTags: ICTag[][] = []

    games.forEach((currentGame) => {
      populatedTags[currentGame.id] = currentGame.tags!.map(
        (currentTag) => (tags ?? []).find((tag) => tag.id === currentTag)!,
      )
    })

    return populatedTags
  }, [tags])

  const useGameList = useMemo(() => {
    return applyFilters(games, search, optimizeTags)
  }, [games, search])

  return (
    <GamesList
      navigation={{
        to: GAME_ROUTE,
        params: {
          communityId: String(params?.communityId ?? 0),
        },
      }}
      games={useGameList}
      onPageChange={onChange}
      communityId={communityId}
      page={search.page ?? 1}
    />
  )
}
