import { <PERSON>, Grid2, <PERSON>, <PERSON>po<PERSON> } from "@mui/material"
import { useRouter } from "@tanstack/react-router"
import React, { useCallback } from "react"
import { z } from "zod"

import { LoadingButtonWithError } from "../../../components/LoadingButtonWithError/LoadingButtonWithError"
import { UserAvatar } from "../../../components/UserAvatar/UserAvatar"
import { RoleData, hasCommunityRole, hasPermission } from "../../../permissions"
import { COMMUNITY_OPEN_ROUTE } from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"
import { trpc } from "../../../trpc/trpc"

import { ShareGames } from "./ShareGames"

interface UserInfoProps {
  user: {
    id: number
    name: string
    bggUsername: string | null
    roles: RoleData[]
    shareMyGames: boolean | null
    avatar: string | null
    color: string | null
    gameCount: number | null
  }
  canApprove: boolean
  communityId: number
  canShare: boolean
}

const schema = z.object({
  communityId: z.number(),
  userId: z.number(),
  status: z.enum(["member", "moder", "banned", "rejected"]),
})

export const UserInfo = ({
  user,
  communityId,
  canApprove,
  canShare, // user is admin/moder OR my games and Community Allows share
}: UserInfoProps) => {
  const myData = useUserStore((store) => store.userData)
  const route = useRouter()
  const onSubmit = useCallback(
    async (status: string) => {
      const data = schema.parse({
        communityId,
        userId: user.id,
        status: status,
      })
      await trpc.changeUserStatus.mutate(data)
      route.invalidate()
      return false
    },
    [user.id, communityId, route],
  )

  const onLeave = useCallback(async () => {
    const leave = await trpc.leaveCommunity
      .mutate({ communityId })
      .then(() => true)
      .catch(() => false)
    if (leave) {
      route.invalidate()
      route.navigate({
        to: COMMUNITY_OPEN_ROUTE,
      })
      return true
    } else {
      return false
    }
  }, [user.id, communityId, route])

  const statusError = "Failed to update status"

  const isMember = hasPermission(user, "community", "isMember", {
    id: communityId,
  })

  return (
    <Grid2 container spacing={2} columns={3} sx={{ maxWidth: "400px" }}>
      <Grid2 size={1}>
        <Box>
          <UserAvatar user={user} size="large" />
        </Box>
      </Grid2>
      <Grid2 size={2}>
        <Grid2 columns={2} padding={2} container spacing={2}>
          <Grid2 size={2}>
            <Typography variant="subtitle1" color="textSecondary">
              {"Member Info"}
            </Typography>
          </Grid2>
          <Grid2 size={1}>
            <Typography variant="body1" fontWeight={500}>
              Name
            </Typography>
          </Grid2>
          <Grid2 size={1}>
            <Typography>{user.name}</Typography>
          </Grid2>
          <Grid2 size={1}>
            <Typography variant="body1" fontWeight={500}>
              Owned Games
            </Typography>
          </Grid2>
          <Grid2 size={1}>
            <Typography>{user.gameCount}</Typography>
          </Grid2>
          <Grid2 size={1}>
            <Typography variant="body1" fontWeight={500}>
              Status
            </Typography>
          </Grid2>
          <Grid2 size={1}>
            <Typography variant="body1" fontWeight={500}>
              {!isMember ? "Invited" : "Member"}
            </Typography>
          </Grid2>
          {!canShare && (
            <>
              <Grid2 size={1}>
                <Typography variant="body1" fontWeight={500}>
                  Shared Games
                </Typography>
              </Grid2>
              <Grid2 size={1}>
                <Typography variant="body1" fontWeight={500}>
                  {user.shareMyGames ? "Yes" : "No"}
                </Typography>
              </Grid2>
            </>
          )}
          <Grid2 size={2}>
            {canShare && (
              <ShareGames
                userId={user.id}
                communityId={communityId}
                share={user.shareMyGames ?? false}
              />
            )}
          </Grid2>
          {user.bggUsername && (
            <>
              <Grid2 size={1}>
                <Typography variant="body1" fontWeight={500}>
                  BGG Link
                </Typography>
              </Grid2>
              <Grid2 size={1}>
                <Typography>
                  <Link
                    target="_blank"
                    href={`https://boardgamegeek.com/user/${user.bggUsername}`}
                    underline="none"
                  >
                    Open
                  </Link>
                </Typography>
              </Grid2>
            </>
          )}
          {canApprove && user.id !== myData?.id && (
            <>
              <Grid2 size={2}>
                <Typography variant="body1" fontWeight={500}>
                  Manage membership status
                </Typography>
              </Grid2>
              <Grid2 size={1}>
                <Box
                  gap={1}
                  display="flex"
                  justifyContent="center"
                  flexDirection="column"
                >
                  {!isMember && canApprove && (
                    <>
                      <LoadingButtonWithError<boolean>
                        onClick={async () => await onSubmit("member")}
                        errorMessage={statusError}
                        title="Approve"
                        color="primary"
                        variant="contained"
                      />
                      <LoadingButtonWithError<boolean>
                        onClick={async () => await onSubmit("rejected")}
                        errorMessage={statusError}
                        title="Reject"
                        color="secondary"
                        variant="contained"
                      />
                    </>
                  )}
                  <LoadingButtonWithError<boolean>
                    onClick={async () => await onSubmit("banned")}
                    errorMessage={statusError}
                    title="Ban"
                    color="error"
                    variant="contained"
                  />
                  {hasPermission(myData, "community", "promoteModer", {
                    id: communityId,
                  }) &&
                    (hasCommunityRole(user.roles, "moder", communityId) ? (
                      <LoadingButtonWithError<boolean>
                        onClick={async () => await onSubmit("member")}
                        errorMessage={statusError}
                        title="Remove Moderator Status"
                        color="error"
                        variant="contained"
                      />
                    ) : (
                      <LoadingButtonWithError<boolean>
                        onClick={async () => await onSubmit("moder")}
                        errorMessage={statusError}
                        title="Set as Moderator"
                        color="error"
                        variant="contained"
                      />
                    ))}
                </Box>
              </Grid2>
            </>
          )}
          {user.id === myData?.id && (
            <Grid2 size={2}>
              <LoadingButtonWithError
                title="Leave Community"
                onClick={onLeave}
                color="error"
                variant="contained"
                errorMessage="Failed to leave community"
              />
            </Grid2>
          )}
        </Grid2>
      </Grid2>
    </Grid2>
  )
}
