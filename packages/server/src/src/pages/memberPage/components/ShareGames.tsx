import { Box, FormControlLabel, Switch, Typography } from "@mui/material"
import React, { useCallback } from "react"

import {
  LoaderDialog,
  LoaderDialogState,
} from "../../../components/LoaderDialog/LoaderDialog"
import { trpc } from "../../../trpc/trpc"

interface ShareGamesProps {
  share: boolean
  communityId: number
  userId: number
}
export const ShareGames = ({ share, userId, communityId }: ShareGamesProps) => {
  const [loading, setLoading] = React.useState<LoaderDialogState>(null)
  const onChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setLoading("loading")
      const value = event.target.checked
      trpc.switchGameShare
        .mutate({
          communityId,
          userId,
          share: value,
        })
        .then(() => {
          setLoading(null)
        })
        .catch(() => {
          setLoading("failed")
        })
    },
    [userId, communityId],
  )

  return (
    <>
      <FormControlLabel
        control={<Switch onChange={onChange} defaultChecked={share} />}
        label="Share games in this community"
      />
      <Box>
        <Typography variant="caption" color="textSecondary">
          If this is turned on - all games will be visible for other users in
          this community.
        </Typography>
      </Box>
      <LoaderDialog
        state={loading}
        onClose={() => setLoading(null)}
        failMessage={"Failed to change share setting"}
      />
    </>
  )
}
