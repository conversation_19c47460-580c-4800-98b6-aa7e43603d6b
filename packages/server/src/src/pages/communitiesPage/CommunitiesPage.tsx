import { useEffect } from "react"

import { CommunityList } from "../../components/communityList/CommunityList"
import { createRoutes } from "../../routes/breadcrumbs"
import { communityListRoute } from "../../routes/community.route"
import { useBreadcrumbsStore } from "../../store/useBreadcrumbsStore"
import { RouterOutput } from "../../trpc/trpc"

type CommunityListData = RouterOutput["communityList"]

export const CommunitiesPage = () => {
  const communities: CommunityListData | null =
    communityListRoute.useLoaderData()
  const setBreadcrumbs = useBreadcrumbsStore((state) => state.setBreadcrumbs)

  useEffect(() => {
    createRoutes(setBreadcrumbs, "communitiesRoute", {
      indexRoute: {},
      communitiesRoute: {},
    })
  }, [])

  if (!communities) {
    return <></>
  }

  return <CommunityList title="My Communities" communities={communities} />
}
