import { useEffect } from "react"

import { createRoutes } from "../routes/breadcrumbs"
import { useBreadcrumbsStore } from "../store/useBreadcrumbsStore"

export const _blankPage = () => {
  const setBreadcrumbs = useBreadcrumbsStore((state) => state.setBreadcrumbs)

  useEffect(() => {
    createRoutes(
      setBreadcrumbs,
      "indexRoute",
      {
        indexRoute: {},
      },
      {},
    )
  }, [])

  return <div>Content</div>
}
