import { Box, Typography } from "@mui/material"
import {
  type NotFoundError,
  type NotFoundRouteProps,
} from "@tanstack/react-router"

import { isErrorMessage } from "../../routes/handleLoaderErrors"

import * as styles from "./generalErrorPage.css"

export const GeneralErrorPage = (props: NotFoundRouteProps) => {
  const useData = ((props?.data ?? "") as unknown as NotFoundError).data ?? ""

  return (
    <Box className={styles.container}>
      <Typography variant="h6" color="error">
        {isErrorMessage(useData) ? useData.message : "Failed to load data!"}
      </Typography>
    </Box>
  )
}
