import { ArrayElement, RouterInput, RouterOutput } from "../trpc/trpc"

// CommunityExpanded

export type ICommunityExpanded = RouterOutput["communityExpanded2"]
export type IGames = RouterOutput["communityExpanded2"]["games"]
export type IGame = ArrayElement<IGames>

export type IOwners = ArrayElement<IGames>["users"]
export type IOwner = ArrayElement<IOwners>

export type ICommunityUsers = RouterOutput["communityExpanded2"]["users"]
export type ICommunityUser = ArrayElement<ICommunityUsers>

export type IGamesAll = RouterOutput["communityGamesList2"]

export type ICommunityGame = ArrayElement<IGamesAll["games"]>

export type IGeneralGame = ICommunityGame | IGame

export type ITags = IGamesAll["tags"]

export type ITag = ArrayElement<ITags>

export type ITagCategories =
  RouterOutput["communityGamesList2"]["tagCategories"]
export type ITagCategory = ArrayElement<ITagCategories>

export type ICommunityListGames = RouterOutput["communityGamesList2"]["games"]
export type ICommunityListGame = ArrayElement<ICommunityListGames>

export type IGameUsers = RouterOutput["communityGamesList2"]["users"]
export type IGameUser = ArrayElement<IGameUsers>

export type IGameGameUsers =
  RouterOutput["communityGameExtended2"]["game"]["users"]
export type IGameGameUser = ArrayElement<IGameGameUsers>

export type IGameGame = RouterOutput["communityGameExtended2"]["game"]

export type IGameViewTags = RouterOutput["communityGameExtended2"]["tags"]
export type IGameViewTag = ArrayElement<IGameViewTags>

export type IGameViewUsers =
  RouterOutput["communityGameExtended2"]["game"]["users"]

export type IGameViewUser = ArrayElement<IGameViewUsers>

export type IGameViewExpansions =
  RouterOutput["communityGameExtended2"]["expansions"]

export type IGameViewExpansion = ArrayElement<IGameViewExpansions>

export type IAuthUserData = RouterOutput["loggedInUser"]

export function isCommunityGame(data: IGeneralGame): data is ICommunityGame {
  return (data as ICommunityGame).average !== undefined
}

export type IOUserProfileUpdate = RouterInput["userProfileUpdate"]

export type IUserProfileGames = RouterOutput["getMyInfo"]["games"]

export type IUserProfileGame = ArrayElement<IUserProfileGames>

export type IBasicCommunity = RouterOutput["communityBasic"]

export type IUserProfileGameDetails = RouterOutput["getMyGameInfo"]["game"]

export type IUserProfileGameExpansions =
  RouterOutput["getMyGameInfo"]["expansions"]

export type IUserProfileGameData = RouterOutput["getMyGameInfo"]["myData"]

export type IUserProfileGameExpansion = ArrayElement<IUserProfileGameExpansions>
