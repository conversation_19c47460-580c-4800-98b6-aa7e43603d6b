import { Avatar } from "@mui/material"
import classnames from "classnames"
import React, { useMemo } from "react"

import { PROFILE_IMAGES } from "../../config/images"
import { getInitials } from "../../utils/transformText"

import * as styles from "./userAvatar.css"

export interface ICAvatarUser {
  name: string
  id: number
  avatar: string | null
  color: string | null
}

export type ICFAvatarOnClick = (user: number) => void
interface UserAvatarProps {
  user: ICAvatarUser
  size?: "small" | "large"
  onClick?: ICFAvatarOnClick
}

export const UserAvatar = ({
  user,
  size = "small",
  onClick,
}: UserAvatarProps) => {
  const { id, name, avatar, color } = user

  return useMemo(
    () => (
      <Avatar
        alt={name}
        src={
          avatar
            ? `${ENV_IMAGE_CDN}${PROFILE_IMAGES}/${size}_${avatar}`
            : undefined
        }
        title={name}
        sx={{
          backgroundColor: avatar ? "#ffffff" : `#${color}`,
          color: "#000000",
        }}
        className={classnames(styles.avatar, {
          [styles.large]: size === "large",
        })}
        onClick={() => (onClick ? onClick(id) : undefined)}
      >
        {avatar ? "" : getInitials(name)}
      </Avatar>
    ),
    [id, name, avatar, color],
  )
}
