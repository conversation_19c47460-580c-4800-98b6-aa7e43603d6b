import { But<PERSON> } from "@mui/material"
import {
  GoogleAuthProvider,
  getAuth,
  signInWithPopup,
  signOut,
} from "firebase/auth"

import { useModalStore } from "../../store/useModalStore"
import { useUserStore } from "../../store/useUserStore"
import { firebaseApp } from "../../utils/firebase"
import { LOGIN_MODAL_NAME } from "../modals/LoginModal/LoginModal"

export const LoginButton = () => {
  const openModal = useModalStore((state) => state.openModal)
  const isLoggedIn = useUserStore((state) => state.isLoggedIn)
  const auth = getAuth(firebaseApp)

  const handleLogin = async () => {
    openModal(LOGIN_MODAL_NAME)

    /*
    const provider = new GoogleAuthProvider()
    try {
      await signInWithPopup(auth, provider)
    } catch (error) {
      console.error("Login failed:", error)
    }

     */
  }

  const handleLogout = async () => {
    openModal(LOGIN_MODAL_NAME)
    /*
    try {
      await signOut(auth)
    } catch (error) {
      console.error("Logout failed:", error)
    }

     */
  }

  if (isLoggedIn) {
    return (
      <Button variant="outlined" color="inherit" onClick={() => handleLogout()}>
        Logout
      </Button>
    )
  }

  return (
    <Button variant="outlined" color="inherit" onClick={() => handleLogin()}>
      Sign on/Log In
    </Button>
  )
}
