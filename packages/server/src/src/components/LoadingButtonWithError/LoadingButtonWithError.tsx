import { LoadingButton, type LoadingButtonProps } from "@mui/lab"
import { useState } from "react"

import { LoaderDialog } from "../LoaderDialog/LoaderDialog"

interface LoadingButtonWithErrorProps<S> extends LoadingButtonProps {
  onClick: () => Promise<S>
  onSuccess?: (response: S) => void
  onErrorReact?: (response: string) => void
  title: string
  errorMessage?: string
  errorTitle?: string
}

type ComponentProps<S> = React.PropsWithChildren<LoadingButtonWithErrorProps<S>>

export const LoadingButtonWithError = <S,>({
  onClick,
  onErrorReact,
  onSuccess,
  title,
  errorMessage,
  errorTitle,
  ...props
}: ComponentProps<S>) => {
  const [loading, setLoading] = useState(false)
  const [failed, setFailed] = useState(false)

  const handleClick = () => {
    setLoading(true)
    onClick()
      .then((result: S) => onSuccess && onSuccess(result))
      .catch((error: unknown) => {
        setFailed(true)
        if (onErrorReact) {
          onErrorReact(error as string)
        }
      })
      .finally(() => {
        setLoading(false)
      })
  }

  return (
    <>
      <LoadingButton {...props} onClick={handleClick} loading={loading}>
        {title}
      </LoadingButton>
      <LoaderDialog
        state={failed ? "failed" : null}
        title={errorTitle}
        failMessage={errorMessage ?? "Failed!"}
        onClose={() => setFailed(false)}
      />
    </>
  )
}
