import { Box, Paper, Typography } from "@mui/material"
import classNames from "classnames"
import React, { FC } from "react"

import * as styles from "./titleRow.css"

interface TitleRowProps {
  title: string
  children?: React.ReactNode
  className?: string
}
export const TitleRow: FC<TitleRowProps> = ({ className, children, title }) => {
  return (
    <Box className={classNames(styles.titleBox, className ?? "")}>
      <Box className={styles.titleWrapper}>
        <Paper elevation={2} className={styles.title}>
          <Box className={styles.nameWrapper}>
            <Typography fontWeight="500" variant="h6" className={styles.name}>
              {title}
            </Typography>
            {children && <Box className={styles.childrenBox}>{children}</Box>}
          </Box>
        </Paper>
      </Box>
    </Box>
  )
}
