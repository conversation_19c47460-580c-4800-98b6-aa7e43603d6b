@value isLargeTablet from "../../css/size.css";

.title {
  padding: var(--spacing-3);
}

.name {
  text-overflow: ellipsis;
  text-wrap-mode: nowrap;
  overflow: hidden;
}

.nameWrapper {
}

.titleWrapper {
  max-width: 100%;
}

.titleBox {
  margin-top: var(--spacing-1);
  display: flex;
  justify-content: flex-end;
}

@media (max-width: isLargeTablet) {
  .titleBox {
    margin-top: var(--spacing-5);
  }
}

.childrenBox {
  padding-top: var(--spacing-2);
  display: flex;
  justify-content: flex-end;
}