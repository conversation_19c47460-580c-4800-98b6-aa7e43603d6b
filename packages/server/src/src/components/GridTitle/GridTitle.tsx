import { Box, Typography } from "@mui/material"

import { PartyLink } from "../elements/link/PartyLink/PartyLink"

interface GridTitleProps {
  title: string
  route: string
  nextTitle: string
  params: {
    [key: string]: string
  }
}

export const GridTitle = ({
  title,
  route,
  nextTitle,
  params,
}: GridTitleProps) => (
  <Box
    display="flex"
    flexDirection="row"
    width="100%"
    justifyContent="flex-start"
    marginBottom={2}
  >
    <Typography variant="h5">{title}</Typography>
    <PartyLink to={route} params={params}>
      {nextTitle}
    </PartyLink>
  </Box>
)
