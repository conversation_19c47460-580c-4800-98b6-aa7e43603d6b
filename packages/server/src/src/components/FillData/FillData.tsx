import { Fade, Snackbar } from "@mui/material"
import { useState } from "react"

import { PROFILE_ROUTE } from "../../routes/paths"
import { useUserStore } from "../../store/useUserStore"
import { PartyLink } from "../elements/link/PartyLink/PartyLink"

export const FillData = () => {
  const userData = useUserStore((state) => state.userData)

  const [open, setOpen] = useState(userData.isNew)

  const handleClose = () => {
    setOpen(false)
  }

  const action = (
    <PartyLink
      color="primary"
      variant="outlined"
      size="small"
      to={PROFILE_ROUTE}
      onClick={handleClose}
    >
      Open Profile
    </PartyLink>
  )

  return (
    <Snackbar
      anchorOrigin={{ vertical: "top", horizontal: "center" }}
      open={open}
      onClose={handleClose}
      TransitionComponent={Fade}
      action={action}
      autoHideDuration={5000}
      message="Please fill Your profile data"
    />
  )
}
