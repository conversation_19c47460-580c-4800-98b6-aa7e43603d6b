import ReportGmailerrorredIcon from "@mui/icons-material/ReportGmailerrorred"
import { Box, CardContent, Typography } from "@mui/material"

import { RoleData } from "../../../../../common/src/permissions/roles/helpers/types"
import { hasCommunityRole, hasPermission } from "../../permissions"
import { COMMUNITY_USER_ROUTE } from "../../routes/paths"
import { useUserStore } from "../../store/useUserStore"
import { ICAvatarUser, UserAvatar } from "../UserAvatar/UserAvatar"
import { PartyCard } from "../elements/link/PartyCard/PartyCard"

import * as styles from "./useRow.css"

type ICUserRow = ICAvatarUser & {
  roles?: RoleData[]
  gameCount?: number | null
}

interface UserRowProps {
  user: ICUserRow
  communityId: string
  onClick: (user?: number) => void
}
export const UserRow = ({ user, onClick, communityId }: UserRowProps) => {
  const myData = useUserStore((store) => store.userData)
  return (
    <PartyCard
      className={styles.card}
      to={COMMUNITY_USER_ROUTE}
      params={{ userId: String(user.id), communityId: String(communityId) }}
      title={user.name}
    >
      <CardContent>
        <Box display="flex" justifyContent="center">
          <UserAvatar size="large" user={user} onClick={onClick} />
        </Box>
        <Box
          pt={2}
          display="flex"
          justifyContent="center"
          alignItems="center"
          flexDirection="column"
        >
          <Typography variant="h6">{user.name}</Typography>
          {user.gameCount && <Typography>{user.gameCount} games</Typography>}
        </Box>
      </CardContent>
      {hasCommunityRole(user.roles ?? [], "invited", parseInt(communityId)) &&
        hasPermission(myData, "community", "approve", {
          id: parseInt(communityId),
        }) && (
          <Box title="Requires approval" className={styles.invited}>
            <ReportGmailerrorredIcon color="info" fontSize="large" />
          </Box>
        )}
    </PartyCard>
  )
}
