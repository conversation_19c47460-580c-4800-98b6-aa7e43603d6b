import { InputLabel } from "@mui/material"
import {
  LinkBubbleMenu,
  MenuButtonAlignCenter,
  MenuButtonAlignJustify,
  MenuButtonAlignLeft,
  MenuButtonAlignRight,
  MenuButtonBlockquote,
  MenuButtonBold,
  MenuButtonBulletedList,
  MenuButtonEditLink,
  MenuButtonHighlightColor,
  MenuButtonItalic,
  MenuButtonOrderedList,
  MenuButtonRedo,
  MenuButtonRemoveFormatting,
  MenuButtonStrikethrough,
  MenuButtonSubscript,
  MenuButtonSuperscript,
  MenuButtonTextColor,
  MenuButtonUnderline,
  MenuButtonUndo,
  MenuControlsContainer,
  MenuDivider,
  MenuSelectHeading,
  RichTextEditor,
  type RichTextEditorRef,
  TableBubbleMenu,
} from "mui-tiptap"
import React, { useRef } from "react"
import { Controller, useFormContext } from "react-hook-form"

import { config } from "./config"

interface FormInputProps {
  label: string
  name: string
  helper?: string
  required?: boolean
}

export const RTEdit = ({ label, name, required = false }: FormInputProps) => {
  const rteRef = useRef<RichTextEditorRef>(null)
  const { control } = useFormContext()
  // rteRef.current?.editor?.getHTML()
  return (
    <Controller
      name={name}
      control={control}
      rules={{ required }}
      render={({ field: { onChange, value } }) => (
        <>
          <InputLabel>{label}</InputLabel>
          <RichTextEditor
            ref={rteRef}
            extensions={config}
            onUpdate={() => onChange(rteRef.current?.editor?.getHTML())}
            content={value}
            renderControls={() => (
              <MenuControlsContainer>
                <MenuButtonUndo />
                <MenuButtonRedo />
                <MenuDivider />
                <MenuButtonRemoveFormatting />
                <MenuSelectHeading />
                <MenuButtonTextColor />
                <MenuButtonHighlightColor />
                <MenuDivider />
                <MenuButtonBold />
                <MenuButtonItalic />
                <MenuButtonUnderline />
                <MenuButtonStrikethrough />
                <MenuButtonSubscript />
                <MenuButtonSuperscript />
                <MenuDivider />
                <MenuButtonAlignLeft />
                <MenuButtonAlignCenter />
                <MenuButtonAlignRight />
                <MenuButtonAlignJustify />
                <MenuDivider />
                <MenuButtonBlockquote />
                <MenuDivider />
                <MenuButtonBulletedList />
                <MenuButtonOrderedList />
                <MenuDivider />
                <MenuButtonEditLink />
                <MenuDivider />
              </MenuControlsContainer>
            )}
          >
            {() => (
              <>
                <LinkBubbleMenu />
                <TableBubbleMenu />
              </>
            )}
          </RichTextEditor>
        </>
      )}
    />
  )
}
