import {
  Box,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Typography,
} from "@mui/material"
import React, { FC } from "react"
import { Controller, useFormContext } from "react-hook-form"

interface FormInputProps {
  label: string
  name: string
  placeholder?: string
  helper?: string
  required?: boolean
  multiline?: boolean
  items: {
    title: string
    value: string
  }[]
}

export const FormSelect: FC<FormInputProps> = ({
  label,
  name,
  helper = "",
  items,
}) => {
  const { control } = useFormContext()
  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value }, fieldState: { error } }) => (
        <>
          <FormControl error={!!error}>
            <InputLabel id={`${name}-label-id`}>{label}</InputLabel>
            <Select
              labelId={`${name}-label-id`}
              value={value}
              label={label}
              onChange={onChange}
            >
              {items.map((item) => (
                <MenuItem key={item.value} value={item.value}>
                  {item.title}
                </MenuItem>
              ))}
            </Select>
            {helper && (
              <Box>
                <Typography
                  variant="caption"
                  color="textSecondary"
                  dangerouslySetInnerHTML={{ __html: helper }}
                />
              </Box>
            )}
          </FormControl>
        </>
      )}
    />
  )
}
