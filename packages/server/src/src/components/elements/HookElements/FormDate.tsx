import { Box, Typography } from "@mui/material"
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers"
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs"
import dayjs from "dayjs"
import React, { FC } from "react"
import { Controller, useFormContext } from "react-hook-form"

interface FormInputProps {
  label: string
  name: string
  helper?: string
  required?: boolean
  minDate?: Date
}

export const FormDate: FC<FormInputProps> = ({
  label,
  name,
  helper = "",
  required = false,
  minDate,
}) => {
  const { control } = useFormContext()
  return (
    <Controller
      name={name}
      control={control}
      rules={{ required }}
      render={({ field: { onChange, value } }) => (
        <>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
              label={label}
              onChange={(value) => onChange(value?.format("YYYY-MM-DD"))}
              value={dayjs(value)}
              minDate={minDate ? dayjs(minDate) : undefined}
            />
          </LocalizationProvider>
          {helper && (
            <Box>
              <Typography variant="caption" color="textSecondary">
                {helper}
              </Typography>
            </Box>
          )}
        </>
      )}
    />
  )
}
