import { Card, type CardProps } from "@mui/material"
import { type LinkComponent, createLink } from "@tanstack/react-router"
import { forwardRef } from "react"

const MuiCardComponent = forwardRef<HTMLAnchorElement, CardProps>(
  (props, ref) => {
    return <Card component={"div"} ref={ref} {...props} />
  },
)

const CreatedCardComponent = createLink(MuiCardComponent)

export const PartyCard: LinkComponent<typeof MuiCardComponent> = (props) => {
  return <CreatedCardComponent preload={"intent"} {...props} />
}
