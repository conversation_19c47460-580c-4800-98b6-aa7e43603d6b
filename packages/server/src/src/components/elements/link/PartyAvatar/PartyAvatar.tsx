import { Avatar, type AvatarProps } from "@mui/material"
import { type LinkComponent, createLink } from "@tanstack/react-router"
import { forwardRef } from "react"

const MuiAvatarComponent = forwardRef<HTMLAnchorElement, AvatarProps>(
  (props, ref) => {
    return <Avatar component={"div"} ref={ref} {...props} />
  },
)

const CreatedAvatarComponent = createLink(MuiAvatarComponent)

export const PartyAvatar: LinkComponent<typeof MuiAvatarComponent> = (
  props,
) => {
  return <CreatedAvatarComponent preload={"intent"} {...props} />
}
