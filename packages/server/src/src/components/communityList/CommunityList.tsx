import { Box } from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import { FC, useCallback } from "react"

import { COMMUNITY_ROUTE } from "../../routes/paths"
import {
  CommunityThumbnail,
  ICCommunity,
} from "../communityThumbnail/CommunityThumbnail"
import { CreateCommunity } from "../createCommunity/CreateCommunity"
import { TitleRow } from "../titleRow/TitleRow"

import * as styles from "./communityList.css"

interface CommunityListProps {
  communities: ICCommunity[]
  title: string
}
export const CommunityList: FC<CommunityListProps> = ({
  communities,
  title,
}) => {
  const navigate = useNavigate()

  const onClick = useCallback(
    (id: number) => {
      navigate({ to: COMMUNITY_ROUTE, params: { communityId: String(id) } })
    },
    [navigate],
  )

  return (
    <>
      <TitleRow title={title}>
        <CreateCommunity />
      </TitleRow>
      <Box className={styles.container}>
        <Box className={styles.list}>
          {communities.map((community) => (
            <CommunityThumbnail
              key={community.id}
              community={community}
              onClick={onClick}
            />
          ))}
        </Box>
      </Box>
    </>
  )
}
