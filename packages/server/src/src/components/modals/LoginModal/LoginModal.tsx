import { memo, useEffect } from "react"
import useChangedProps from "use-changed-props"

import { useModalStore } from "../../../store/useModalStore"

export const LOGIN_MODAL_NAME = "login"

export const LoginModal = memo(() => {
  const { createModal, isOpen, closeModal } = useModalStore((state) => ({
    createModal: state.createModal,
    closeModal: state.closeModal,
    isOpen: state.modalList[LOGIN_MODAL_NAME]?.isOpen ?? false,
  }))

  useChangedProps({ createModal, isOpen, closeModal })

  console.info("NEW XXX")

  console.info(isOpen)

  useEffect(() => {
    console.info("NEW")
    createModal(LOGIN_MODAL_NAME)
  }, [])

  if (!isOpen) {
    return null
  }

  return null
})
