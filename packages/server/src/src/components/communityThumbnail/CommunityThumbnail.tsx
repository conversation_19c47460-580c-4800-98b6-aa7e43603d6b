import CardMembershipIcon from "@mui/icons-material/CardMembership"
import LanguageIcon from "@mui/icons-material/Language"
import MapIcon from "@mui/icons-material/Map"
import {
  Box,
  Card,
  CardContent,
  CardMedia,
  Link,
  Typography,
} from "@mui/material"

import { COMMUNITY_IMAGES } from "../../config/images"

import * as styles from "./communityThumbnail.css"

export interface ICCommunity {
  name: string
  id: number
  image: string
  location?: string | null
  online?: string | null
  openness: string
  approval?: boolean
  share?: boolean
  member?: boolean
}
interface CommunityThumbnailProps {
  community: ICCommunity
  onClick: (id: number) => void
}
export const CommunityThumbnail = ({
  community,
  onClick,
}: CommunityThumbnailProps) => {
  const preventWeirdLink = (e: React.MouseEvent) => {
    e.stopPropagation()
  }
  return (
    <Card onClick={() => onClick(community.id)} className={styles.card}>
      <CardMedia
        component="img"
        className={styles.image}
        src={
          community.image
            ? `${ENV_IMAGE_CDN}${COMMUNITY_IMAGES}/large_${community.image}`
            : `${ENV_IMAGE_CDN}/${ENV_COMMUNITY_DEFAULT_LOGO}`
        }
      />
      <CardContent className={styles.text}>
        <Typography>{community.name}</Typography>
        {community.openness && (
          <Box
            className={styles.openness}
            display="flex"
            justifyContent="center"
            alignItems="center"
            title={community.openness.toLocaleUpperCase()}
          >
            <Typography variant="caption">
              {community.openness.toLocaleUpperCase()}
            </Typography>{" "}
          </Box>
        )}
      </CardContent>

      {community.member && (
        <Box
          className={styles.member}
          display="flex"
          justifyContent="center"
          alignItems="center"
          title="You are a member"
        >
          <CardMembershipIcon color="info" fontSize="large" />
        </Box>
      )}
      {community.location && (
        <Box
          className={styles.map}
          display="flex"
          justifyContent="center"
          alignItems="center"
          title={community.location}
        >
          <Typography variant="caption">{community.location}</Typography>{" "}
          <MapIcon />
        </Box>
      )}
      {community.online && (
        <Box
          className={styles.online}
          display="flex"
          justifyContent="center"
          alignItems="center"
          title={community.online}
        >
          <Link
            href={community.online}
            underline="none"
            variant="caption"
            target="_blank"
            onClick={preventWeirdLink}
          >
            Open link
          </Link>{" "}
          <LanguageIcon />
        </Box>
      )}
    </Card>
  )
}
