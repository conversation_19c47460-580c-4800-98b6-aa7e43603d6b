.card {
  cursor: pointer;
  position: relative;
}

.location {
  position: absolute;
  height: 22px;
  background-color: white;
  padding: 2px 2px 2px 4px;
  border-radius: 10px;
}

.text {
  position: relative;
}

.online {
  composes: location;
  top: 4px;
  right: 4px;
}

.map {
  composes: location;
  top: 34px;
  right: 4px;
}

.openness {
  composes: location;
  left: 4px;
  bottom: Calc(100% + 4px);
  padding: 2px 8px;
}

.member {
  composes: location;
  height: 40px;
  width: 40px;
  border-radius: 50%;
  padding: 2px;
  top: 4px;
  left: 4px;
}

.image {
  width: 250px;
  height: 250px;
  object-fit: cover;
}