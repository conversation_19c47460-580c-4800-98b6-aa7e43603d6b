import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from "@mui/material"
import React, { memo, useCallback, useEffect, useMemo, useState } from "react"

import { DEFAULT_ORDER, SEARCH_STR_LENGTH } from "../../config/game.conf"
import { useIsMobileStore } from "../../store/useIsMobileStore"

import { ICTagCat } from "./components/ChipList"
import { Order } from "./components/Order"
import { PlayerCountFilter } from "./components/PlayerCountFilter"
import { ICSearchBoxTag, SearchBox } from "./components/SearchBox"
import * as styles from "./gameSearch.css"

export interface SearchParams {
  search?: string | undefined
  orderBy?: string | undefined
  page?: number | undefined
  order?: string | undefined
  minPlayers?: number | undefined
  maxPlayers?: number | undefined
  playerLevel?: number | undefined
}

interface GameSearchProps {
  tags?: ICSearchBoxTag[]
  tagCategories?: ICTagCat[]
  onNavigate: (search: SearchParams) => void
  search: SearchParams
  personalOrder?: boolean
}

export const GameSearch = memo(
  ({
    tags,
    tagCategories,
    onNavigate,
    search,
    personalOrder = false,
  }: GameSearchProps) => {
    const sizeThresholdList = useIsMobileStore(
      (state) => state.sizeThresholdList,
    )
    const [open, setOpen] = useState(false)

    const doNavigate = useCallback(
      (actions: Partial<typeof search>) => {
        const searchString =
          actions.search !== undefined
            ? actions.search.length >= SEARCH_STR_LENGTH
              ? actions.search
              : ""
            : search.search

        const newSearch: typeof search = {
          page: actions.search ? 1 : search.page,
          search: searchString,
          order: actions.order ?? search.order,
          orderBy: actions.search
            ? "search"
            : (actions.orderBy ?? search.orderBy),
        }

        if (
          (actions.minPlayers || search.minPlayers) &&
          actions.minPlayers !== -1
        ) {
          newSearch.minPlayers = actions.minPlayers ?? search.minPlayers
          newSearch.playerLevel = search.playerLevel ?? 0
        }

        if (
          (actions.maxPlayers || search.maxPlayers) &&
          actions.maxPlayers !== -1
        ) {
          newSearch.maxPlayers = actions.maxPlayers ?? search.maxPlayers
          newSearch.playerLevel = search.playerLevel ?? 0
        }

        if (actions.playerLevel !== undefined) {
          newSearch.playerLevel = actions.playerLevel
        }

        onNavigate(newSearch)
      },
      [search],
    )

    useEffect(() => {
      if (!sizeThresholdList.largeTablet) {
        setOpen(false)
      }
    }, [sizeThresholdList, setOpen])

    const onChange = (orderBy: string) => doNavigate({ orderBy })

    const onChangeMin = (min: number) => doNavigate({ minPlayers: min })

    const onChangeMax = (max: number) => doNavigate({ maxPlayers: max })

    const onSetPlayerLevel = (value: number) => {
      doNavigate({ playerLevel: value })
    }

    const onSearch = (search: string) => doNavigate({ search })

    const onSwap = () =>
      doNavigate({
        order: (search.order ?? DEFAULT_ORDER) === "asc" ? "desc" : "asc",
      })

    const filterAndCount = useMemo(
      () => (
        <>
          <Order
            orderBy={search.orderBy}
            onChange={onChange}
            personalOrder={personalOrder}
            onSwap={onSwap}
            order={search.order}
          />
          <PlayerCountFilter
            onChangeMax={onChangeMax}
            onChangeMin={onChangeMin}
            playerLevel={search.playerLevel}
            onSetPlayerLevel={onSetPlayerLevel}
            maxPlayers={search.maxPlayers}
            minPlayers={search.minPlayers}
          />
        </>
      ),
      [
        search.orderBy,
        search.order,
        search.minPlayers,
        search.maxPlayers,
        search.playerLevel,
      ],
    )

    return (
      <Box className={styles.container}>
        <SearchBox
          onSearch={onSearch}
          search={search.search}
          tags={tags}
          tagCategories={tagCategories}
        />
        {sizeThresholdList.largeTablet && (
          <Button
            className={styles.moreButton}
            variant="outlined"
            onClick={() => setOpen(true)}
          >
            More
          </Button>
        )}
        {sizeThresholdList.largeTablet && (
          <Dialog open={open} fullWidth>
            <DialogTitle>Sort and filter</DialogTitle>
            <DialogContent>
              <Box className={styles.dialogContent}>{filterAndCount}</Box>
            </DialogContent>
            <DialogActions>
              <Button variant="outlined" onClick={() => setOpen(false)}>
                Close
              </Button>
            </DialogActions>
          </Dialog>
        )}
        {!sizeThresholdList.largeTablet && filterAndCount}
      </Box>
    )
  },
)
