import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward"
import <PERSON>U<PERSON>wardIcon from "@mui/icons-material/ArrowUpward"
import FilterListIcon from "@mui/icons-material/FilterList"
import {
  Box,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  type SelectChangeEvent,
} from "@mui/material"
import React from "react"

import { DEFAULT_ORDER, DEFAULT_ORDER_BY } from "../../../config/game.conf"

import * as styles from "./order.css"

interface OrderProps {
  orderBy?: string
  onChange: (orderBy: string) => void
  personalOrder: boolean
  onSwap: () => void
  order?: string
}

export const Order = ({
  orderBy,
  onChange,
  personalOrder,
  order,
  onSwap,
}: OrderProps) => {
  const handleChange = (event: SelectChangeEvent<string>) =>
    onChange(event.target.value)

  return (
    <Box className={styles.container}>
      <FormControl fullWidth>
        <InputLabel id="order-by-label">Order by</InputLabel>
        <Select
          labelId="order-by-label"
          id="order-by-select"
          value={orderBy ?? DEFAULT_ORDER_BY}
          label="Order by"
          onChange={handleChange}
        >
          <MenuItem value="title">Title</MenuItem>
          <MenuItem value="average">BGG score</MenuItem>
          {personalOrder && <MenuItem value="rating">User score</MenuItem>}
          {personalOrder && <MenuItem value="lastPlay">Last Play</MenuItem>}
          {personalOrder && <MenuItem value="playCount">Play Count</MenuItem>}
          <MenuItem value="news">Newest</MenuItem>
          <MenuItem value="weight">Weight</MenuItem>
          <MenuItem value="search">Search results</MenuItem>
          <MenuItem value="id">Id</MenuItem>
        </Select>
      </FormControl>
      <FormControl fullWidth>
        <Button
          variant="outlined"
          size="small"
          onClick={onSwap}
          startIcon={<FilterListIcon />}
          endIcon={
            (order ?? DEFAULT_ORDER) === "desc" ? (
              <ArrowUpwardIcon />
            ) : (
              <ArrowDownwardIcon />
            )
          }
        ></Button>
      </FormControl>
    </Box>
  )
}
