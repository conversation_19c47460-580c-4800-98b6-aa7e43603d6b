import { Box, FormControl, Paper, TextField } from "@mui/material"
import classnames from "classnames"
import debounce from "debounce"
import Fuse from "fuse.js"
import React, {
  ChangeEvent,
  KeyboardEvent,
  memo,
  useEffect,
  useMemo,
  useState,
} from "react"

import { SEARCH_STR_LENGTH } from "../../../config/game.conf"
import { ITag } from "../../../types/tRPC.types"
import { fixedTags } from "../constants"

import { ChipList, ICTag, ICTagCat } from "./ChipList"
import * as styles from "./searchBox.css"

export type ICSearchBoxTag = ICTag & {
  id: number
}

interface SearchBoxProps {
  onSearch: (search: string) => void
  search: string | undefined
  tags?: ICSearchBoxTag[]
  tagCategories?: ICTagCat[]
}
export const SearchBox = memo(
  ({ onSearch, search, tags, tagCategories }: SearchBoxProps) => {
    const [foundTags, setFoundTags] = useState<ITag[]>([])
    const [showModal, setShowModal] = useState<boolean>(false)

    const [searchText, setSearchText] = useState<string | undefined>(search)

    useEffect(() => {
      if (tags) {
        if (search?.length ?? 0 >= SEARCH_STR_LENGTH) {
          const options = {
            minMatchCharLength: SEARCH_STR_LENGTH,
            threshold: 0.6,
            findAllMatches: true,
            keys: ["title"],
          }

          const searchInstance = new Fuse(
            tags.filter((tag) => tag.type !== 1 && tag.type !== 2),
            options,
          )

          const searchProps = (search ?? "")
            .split(" ")
            .filter((s) => s.length >= SEARCH_STR_LENGTH)
            .join(" | ")

          const result = searchInstance.search(searchProps)

          const gamesResult = result.map((result) => {
            return result.item
          })

          setFoundTags(gamesResult)
        } else {
          setFoundTags([])
        }
      }
    }, [search, tags])

    const handleSearch = (event: ChangeEvent<HTMLInputElement>) =>
      onSearch(event.target.value)

    const onChip = (clicked: string) => {
      onSearch(clicked)
      setSearchText(clicked)
    }

    const onSearchDelayed = useMemo(
      () => debounce(handleSearch, 200),
      [onSearch],
    )

    const onSearchCommon = (event: ChangeEvent<HTMLInputElement>) => {
      onSearchDelayed(event)
      setSearchText(event.target.value)
    }

    const onKeyDown = (e: KeyboardEvent<HTMLDivElement>) => {
      if (e.key === "Escape") {
        setSearchText("")
        onSearch("")
      }
    }

    return (
      <Box position="relative">
        <FormControl fullWidth>
          <TextField
            value={searchText}
            id="search-games"
            label="Search"
            variant="outlined"
            onChange={onSearchCommon}
            onKeyDown={onKeyDown}
            onFocus={() => setShowModal(true)}
            onBlur={debounce(() => setShowModal(false), 500)}
          />
        </FormControl>
        {tags && tagCategories && (
          <Box
            className={classnames(styles.searchTags, {
              [styles.searchTagsVisible]: showModal,
            })}
          >
            <Paper>
              {foundTags.length > 0 && (
                <ChipList
                  onChip={onChip}
                  tagCategories={tagCategories}
                  tagList={foundTags.slice(0, 6)}
                />
              )}
              <ChipList
                onChip={onChip}
                tagCategories={tagCategories}
                tagList={fixedTags}
              />
            </Paper>
          </Box>
        )}
      </Box>
    )
  },
)
