@value isTablet from "../../css/size.css";

.container {
    display:flex;
    padding: var(--spacing-2);
    gap: var(--spacing-2);
    width: 100%;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    box-sizing: border-box;
    max-width: 700px;
    align-items: center;
}

.dialogContent {
    align-content: center;
    justify-content: center;
    max-width: 300px;
    margin: auto;
    gap: var(--spacing-3);
    display: flex;
    flex-direction: column;
}

.moreButton {
    max-width: 100px;
}

@media (max-width: isTablet) {
    .container {
        flex-direction: column;
    }
}