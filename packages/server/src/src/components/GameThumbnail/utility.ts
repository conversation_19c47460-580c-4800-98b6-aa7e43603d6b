export const hexdec = function (hexString: string) {
  hexString = hexString.replace(/[^a-f0-9]/gi, "")
  return parseInt(hexString, 16)
}

export const dechex = function (number: number) {
  if (number < 0) {
    number = 0xffffffff + number + 1
  }

  // first let's drop fractions (parseInt) and then convert to string with base 16
  return parseInt(String(number), 10).toString(16)
}

export const calculateRed = (rating: number, max: number = 10) => {
  let r = hexdec("ff")
  let g = hexdec("0")

  const change = (r / (max * 10)) * (rating * 10)

  r -= change
  g = change

  const r2 = dechex(r)
  const g2 = dechex(g)

  const returnR2 = String(r2).length < 2 ? `0${r2}` : r2
  const returnG2 = String(g2).length < 2 ? `0${g2}` : g2

  return `#${returnR2}${returnG2}00`
}
