import CalendarMonthIcon from "@mui/icons-material/CalendarMonth"
import NewReleasesIcon from "@mui/icons-material/NewReleases"
import { Box, CardContent, Grid2, Typography } from "@mui/material"
import { type LinkComponentProps } from "@tanstack/react-router"
import classnames from "classnames"
import dayjs from "dayjs"
import localizedFormat from "dayjs/plugin/localizedFormat"

import { GAME_IMAGES } from "../../config/images"
import { BggLink } from "../BggLink/BggLink"
import { ICAvatarUser, ICFAvatarOnClick } from "../UserAvatar/UserAvatar"
import { UserAvatarGroup } from "../UserAvatarGroup/UserAvatarGroup"
import { PartyCard } from "../elements/link/PartyCard/PartyCard"

import * as styles from "./gameThumbnail.css"
import { calculateRed } from "./utility"

export interface ICThumbnailGame {
  id: number
  title: string
  bggId: number
  rating?: number | null
  average?: number | null
  playCount?: number | null
  lastPlay?: string | null
}

export type ICNavigationProps = Pick<
  LinkComponentProps,
  "to" | "search" | "params"
>

interface GameThumbnailProps {
  game: ICThumbnailGame
  communityId?: number
  onUser?: ICFAvatarOnClick
  userList?: ICAvatarUser[]
  isNew?: boolean
  displayOpen?: boolean
  className?: string
  elevation?: number
  navigation?: ICNavigationProps
}
export const GameThumbnail = ({
  game,
  onUser,
  userList,
  isNew = false,
  className = "",
  elevation = 1,
  navigation,
}: GameThumbnailProps) => {
  dayjs.extend(localizedFormat)
  return (
    <PartyCard
      to={navigation?.to}
      search={navigation?.search}
      params={navigation?.params}
      key={game.id}
      className={classnames(styles.card, className, {
        [styles.pointer]: !!navigation,
      })}
      elevation={elevation}
      title={game.title}
      preload="intent"
      preloadDelay={1000}
    >
      <Box className={styles.imageContainer}>
        <img
          className={styles.image}
          src={`${ENV_IMAGE_CDN}${GAME_IMAGES}/${game.id}.jpg`}
          loading="lazy"
        />
      </Box>
      <CardContent className={styles.cardContent}>
        <Box>
          <Typography>{game.title}</Typography>
        </Box>
        <Grid2 container spacing={2}>
          <Grid2 size={6}>
            <Box display="flex" justifyContent="flex-start">
              {userList && (
                <UserAvatarGroup users={userList} onClick={onUser} />
              )}
            </Box>
          </Grid2>
        </Grid2>
      </CardContent>
      {isNew && (
        <Box className={styles.newGame} title="New">
          <NewReleasesIcon color="primary" fontSize="large" />
        </Box>
      )}
      <Box className={styles.bggLink}>
        <BggLink bggId={game.bggId} />
        {game.average && (
          <Box className={styles.bggRating}>
            <Typography fontWeight={600}>
              {Math.round(game.average * 10) / 10}
            </Typography>
          </Box>
        )}
      </Box>
      {game.rating && game.rating > 0 && (
        <Box
          className={styles.userRating}
          title={`User rating: ${game.rating}`}
        >
          <Typography
            variant="h6"
            fontWeight={500}
            sx={{ color: calculateRed(Math.round(game.rating)) }}
          >
            {game.rating}
          </Typography>
        </Box>
      )}
      {(game.playCount ?? 0) > 0 && (
        <Box
          className={styles.playCount}
          title={`Total plays: ${game.playCount}`}
        >
          <Typography fontWeight={500}>{game.playCount}</Typography>
        </Box>
      )}
      {game.lastPlay && (
        <Box
          className={styles.lastPlay}
          title={`Last played: ${dayjs(game.lastPlay).format("ll")}`}
        >
          <CalendarMonthIcon />
        </Box>
      )}
    </PartyCard>
  )
}
