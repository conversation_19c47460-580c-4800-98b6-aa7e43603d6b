.card {
  width: 250px;
  min-width: 250px;
  height: 250px;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
}

.pointer {
  cursor: pointer;
}

.imageContainer {
  display: flex;
  align-items: center;
  justify-content: center;
}

.image {
  width: 250px;
  height: 250px;
  object-fit: contain;
}

.cardContent {
  background-color: rgba(256, 256, 256, 0.8);
  position: absolute;
  bottom: 0;
  width: 100%;
  box-sizing: border-box;
}

.newGame {
  position: absolute;
  top: 0px;
  left: 0px;
}

.bggLink {
  position: absolute;
  top: 0px;
  right: 0px;
}

.bggRating {
  position: absolute;
  top:0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.bubbles {
  position: absolute;
  cursor: default;
  background-color: #fff;
  border: 1px solid;
top: 8px;
  left: 8px; vertical-align: middle;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  opacity: 0.6;
}

.lastPlay {
  composes: bubbles;
  top: 90px;
  width: 30px;
  height: 30px;
}

.userRating {
  composes: bubbles;
  width: 40px;
  height: 40px;
}

.playCount {
  composes: bubbles;
  top: 54px;
  left: 8px;
  width: 30px;
  height: 30px;
}


.bubbles:hover {
  opacity: 1;
}