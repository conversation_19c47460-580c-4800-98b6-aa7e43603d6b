import { StyledEngineProvider } from "@mui/material"
import isMobile from "is-mobile"
import { useEffect } from "react"

import { useUserDataLoader } from "./hooks/useUserDataLoader"
import { RouterProviderWithContext } from "./routes/router"
import { useIsMobileStore } from "./store/useIsMobileStore"
import { useUserStore } from "./store/useUserStore"

export const App = () => {
  const { userLoaded } = useUserStore()
  const { setIsMobile, setCurrentWidth } = useIsMobileStore()

  useUserDataLoader()

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(isMobile())
      setCurrentWidth(window.innerWidth)
    }
    handleResize()
    window.addEventListener("resize", handleResize)

    return () => {
      window.removeEventListener("resize", handleResize)
    }
  }, [setIsMobile])

  return (
    <StyledEngineProvider injectFirst>
      {userLoaded && <RouterProviderWithContext />}
    </StyledEngineProvider>
  )
}
