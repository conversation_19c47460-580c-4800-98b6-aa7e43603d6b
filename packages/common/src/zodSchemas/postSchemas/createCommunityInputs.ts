import { z } from "zod";

export const createCommunityInputs = z.object({
  id: z.number().optional(),
  name: z.string().min(3),
  openness: z.enum(["public", "publicLimited", "private", "closed"]),
  location: z.union([z.literal(""), z.string().min(3).max(256).optional()]),
  online: z.union([z.literal(""), z.string().min(3).max(256).optional()]),
  approval: z.boolean(),
  allowShare: z.boolean(),
  description: z.union([z.literal(""), z.string().min(10).optional()]),
  welcome: z.union([z.literal(""), z.string().min(1).optional()]),
});

export type CreateCommunityInputs = z.infer<typeof createCommunityInputs>;
