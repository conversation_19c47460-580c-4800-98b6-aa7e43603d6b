import { Permissions } from "./objects/general";
import { Role, RoleSetting, User } from "./roles/helpers/types";

export type PermissionCheck<Key extends keyof Permissions> =
  | boolean
  | ((
      user: User,
      data: Permissions[Key]["dataType"],
      settings?: RoleSetting[],
    ) => boolean);

export type RoleWithPermissions = {
  [Key in keyof Permissions]: Partial<{
    [Action in Permissions[Key]["action"]]: PermissionCheck<Key>;
  }>;
};

export type RolesWithPermissions = {
  [R in Role]: Partial<RoleWithPermissions>;
};
