export type Community = {
  id: number;
  openness?: "public" | "publicLimited" | "private" | "closed";
  viewedUserId?: number;
  allowShare?: boolean;
};

export interface CommunityPermissions {
  dataType: Community;
  action:
    | "isMember"
    | "promoteModer"
    | "view"
    | "update"
    | "delete"
    | "invite"
    | "approve"
    | "viewGeneral"
    | "communityUserData"
    | "shareUserGames";
}
