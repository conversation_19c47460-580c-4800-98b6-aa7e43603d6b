import { Permissions } from "./objects/general";
import { RolesWithPermissions } from "./general";
import { roles } from "./roles/general";
import { isRole, Role, User } from "./roles/helpers/types";
import { Community } from "./objects/community";
import { Userdata } from "./objects/userdata";
import { GlobalData } from "./objects/global";

const log = true;

const logGranter = (
  resource: string,
  action: string,
  granter: Role,
  data?: Community | Userdata | GlobalData,
) => {
  log && console.info("ACCESS GRANTED:", resource, action, data, granter);
};

const logUnGranter = (
  resource: string,
  action: string,
  reason?: string,
  data?: Community | Userdata | GlobalData,
) => {
  log && console.info("ACCESS NOT GRANTED:", resource, action, reason, data);
};

export function hasPermission<Resource extends keyof Permissions>(
  user: User,
  resource: Resource,
  action: Permissions[Resource]["action"],
  data?: Permissions[Resource]["dataType"],
) {
  if (!user.roles || !user.id) {
    logUnGranter(resource, action, "No user information", data);
    return false;
  }
  const result = user.roles.some((role) => {
    if (!isRole(role.role)) return false;

    if (
      role.subject !== "global" &&
      (role.subject !== "community" || role.subjectId !== data?.id)
    ) {
      return false;
    }

    const permission = (roles as RolesWithPermissions)[role.role][resource]?.[
      action
    ];

    if (permission == null) return false;

    if (typeof permission === "boolean") {
      if (permission) {
        logGranter(resource, action, role.role, data);
      }

      return permission;
    }

    const permissionGained =
      data != null && permission(user, data, role.roleSettings);

    if (permissionGained) {
      logGranter(resource, action, role.role, data);
    }

    return permissionGained;
  });

  if (!result) {
    logUnGranter(resource, action, "No appropriate role", data);
  }

  return result;
}
