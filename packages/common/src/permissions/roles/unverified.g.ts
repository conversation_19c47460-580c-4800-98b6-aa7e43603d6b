import { RoleWithPermissions } from "../general";

export const unverified: RoleWithPermissions = {
  community: {
    viewGeneral: (user, community) =>
      community.openness === "publicLimited" || community.openness === "public",
  },
  userdata: {
    view: (user, userData) => user.id === userData.id,
    update: (user, userData) => user.id === userData.id,
    delete: (user, userData) => user.id === userData.id,
  },
  global: {},
};
