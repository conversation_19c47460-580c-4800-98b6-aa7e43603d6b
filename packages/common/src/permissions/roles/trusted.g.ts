import { RoleWithPermissions } from "../general";
import { getSetting } from "./helpers/getSetting";

export const trusted: RoleWithPermissions = {
  community: {
    viewGeneral: (user, community) =>
      community.openness === "publicLimited" || community.openness === "public",
    view: (user, community) =>
      community.openness === "publicLimited" || community.openness === "public",
    shareUserGames: (user, community) => {
      if (community.allowShare) {
        if (user.id === community.viewedUserId) {
          return true;
        }
      }

      return false;
    },
  },
  userdata: {
    view: (user, userData) => user.id === userData.id,
    update: (user, userData) => user.id === userData.id,
    delete: (user, userData) => user.id === userData.id,
  },
  global: {
    isUser: true,
    createCommunity: (user, globalData, settings) => {
      const create = getSetting(settings ?? [], "create_communities_max");
      if (!create) return false;
      if (create === true) return true;

      return Number(create) > (globalData.createdCommunities ?? 0);
    },
  },
};
