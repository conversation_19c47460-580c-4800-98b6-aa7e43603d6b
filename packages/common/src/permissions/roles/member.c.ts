import { RoleWithPermissions } from "../general";

export const member: RoleWithPermissions = {
  community: {
    isMember: true,
    viewGeneral: true,
    view: true,
    communityUserData: true,
    shareUserGames: (user, community) => {
      if (community.allowShare) {
        return false;
      }

      if (user.id !== community.viewedUserId) {
        return false;
      }

      return true;
    },
  },
  userdata: {},
  global: {},
};
