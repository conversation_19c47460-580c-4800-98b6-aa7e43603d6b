import { roles } from "../general";

export type RoleG = "superadmin" | "user" | "unverified" | "admin" | "trusted"; // | "banned" ;
export type RoleC = "owner" | "member" | "invited" | "moder"; // | "disgraced";

export type Subject = "global" | "community" | "userdata";

export type Role = RoleG | RoleC;

export type RoleSetting = {
  name: string;
  value: string;
};

export interface RoleData {
  role: string | Role;
  subject: Subject;
  subjectId: number | null;
  roleSettings?: RoleSetting[];
}

export type User = {
  roles?: RoleData[];
  id?: number;
};

export function isRole(role: Role | string): role is Role {
  return roles[role as Role] !== undefined;
}
