import { Role, RoleC, RoleData, Subject } from "./types";

export const hasSubjectRole = (
  roles: RoleData[],
  role: Role,
  subject: Subject,
  subjectId?: number,
) =>
  roles.findIndex(
    (currentRole) =>
      role === currentRole.role &&
      currentRole.subject === subject &&
      currentRole.subjectId === subjectId,
  ) > -1;

export const hasCommunityRole = (
  roles: RoleData[] | undefined | null,
  role: RoleC | RoleC[],
  subjectId: number,
) => {
  if (!roles) return false;
  if (typeof role !== "string") {
    return role.some((r) => hasSubjectRole(roles, r, "community", subjectId));
  } else {
    return hasSubjectRole(roles, role, "community", subjectId);
  }
};
