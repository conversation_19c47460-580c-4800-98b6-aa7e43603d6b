import { RoleWithPermissions } from "../general";

export const superadmin: RoleWithPermissions = {
  community: {
    viewGeneral: true,
    view: true,
    update: true,
    delete: true,
    approve: true,
    communityUserData: true,
    promoteModer: true,
  },
  userdata: {
    view: true,
    update: true,
    delete: true,
  },
  global: {
    isUser: true,
    createCommunity: true,
    promoteAdmin: true,
  },
};
