import { RolesWithPermissions } from "../general";
import { superadmin } from "./superadmin.g";
import { owner } from "./owner.c";
import { user } from "./user.g";
import { member } from "./member.c";
import { invited } from "./invited.c";
import { moder } from "./moder.c";
import { unverified } from "./unverified.g";
import { admin } from "./admin.g";
import { trusted } from "./trusted.g";

export const roles = {
  superadmin,
  owner,
  user,
  member,
  invited,
  moder,
  unverified,
  admin,
  trusted,
} as const satisfies RolesWithPermissions;
