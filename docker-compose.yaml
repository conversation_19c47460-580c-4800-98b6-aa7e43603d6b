networks:
  frontend:
    ipam:
      config:
        - subnet: **********/24

services:
  pr-nginx:
    image: nginx:1.27.2
    volumes:
      - ./config/nginx/router:/etc/nginx/conf.d/
      - ./data/certbot/conf:/etc/letsencrypt
      - ./data/certbot/www:/var/www/certbot
    ports:
    - 80:80
    - 443:443
    container_name: pr-nginx
    depends_on:
    - pr-main
    - pr-api
    - pr-admin-mysql
    - pr-crons
    links:
    - pr-main
    - pr-api
    - pr-admin-mysql
    - pr-crons
    restart: unless-stopped
    networks:
      frontend:
        ipv4_address: **********
    logging:
      driver: "json-file"
      options:
        max-size: "1k"
        max-file: "3"
    command: "/bin/sh -c 'while :; do sleep 6h & wait $${!}; nginx -s reload; done & nginx -g \"daemon off;\"'"
  pr-run:
    build: ./packages/run
    volumes:
      - ./packages/run/files:/var/www
    restart: unless-stopped
    container_name: pr-run
    depends_on:
      - pr-crons
    networks:
      frontend:
        ipv4_address: ***********
  pr-certbot:
    depends_on:
      - pr-nginx
    image: certbot/certbot
    container_name: pr-certbot
    volumes:
      - ./data/certbot/conf:/etc/letsencrypt
      - ./data/certbot/www:/var/www/certbot
    entrypoint: "/bin/sh -c 'trap exit TERM; while :; do certbot renew; sleep 12h & wait $${!}; done;'"
  pr-db-mysql:
    image: mysql:5.7.24
    restart: always
    networks:
      frontend:
        ipv4_address: **********
    volumes:
      - partymysqldb:/var/lib/mysql
    container_name: pr-db-mysql
    environment:
      MYSQL_PASSWORD: 10IdoSE6umxmu4rPwXsP
      MYSQL_USER: explainer
      MYSQL_ROOT_PASSWORD: sn2D2M0vfuwBy808AiGg
    ports:
      - 3307:3306
    cap_add:
      - SYS_NICE
    logging:
      driver: "json-file"
      options:
        max-size: "1k"
        max-file: "3"
  pr-admin-mysql:
    image: phpmyadmin/phpmyadmin
    container_name: pr-admin-mysql
    restart: unless-stopped
    networks:
      frontend:
        ipv4_address: **********
    environment:
      - PMA_HOST=pr-db-mysql
      - PMA_PORT=3306
      - UPLOAD_LIMIT=1000M
  pr-api:
    build: ./packages/api
    restart: unless-stopped
    volumes:
      - ./packages/common:/usr/src/common
      - ./packages/api/src:/usr/src/app/src
    depends_on:
    - pr-db-mysql
    - pr-memcached
    container_name: pr-api
    networks:
      frontend:
        ipv4_address: **********
    environment:
      - DATABASE_URL=mysql://root:sn2D2M0vfuwBy808AiGg@pr-db-mysql:3306/party
      - AUTH0_ISSUER_BASE_URL=https://party-explain-games.eu.auth0.com/
      - AUTH0_AUDIENCE=https://party.api.explain.games
      - AWS_ACCESS_KEY_ID=********************
      - AWS_SECRET_ACCESS_KEY=neGo2ueWhdf87CXnr1q6YWy6XbkSYc2Z2FPYn+gP
      - S3_BUCKET=community.explain.games
      - AWS_REGION=eu-central-1
      - IMAGE_CDN=https://party.cdn.explain.games
      - MAIN_HOST=https://party.explain.games
      - MYSQL_HOST=pr-db-mysql
      - MYSQL_USER=root
      - MYSQL_PASS=sn2D2M0vfuwBy808AiGg
      - MYSQL_DB=party
      - MYSQL_PORT=3306
      - MEMC_HOST=pr-memcached
      - MEMC_PORT=11211
      - NODE_ENV=production
      - IMAGE_LINK_PROFILE_PICTURES=profilePictures
      - IMAGE_LINK_LOGO=community/logo
  pr-crons:
    build: ./packages/crons
    volumes:
      - ./packages/crons/files:/var/www/html
    depends_on:
      - pr-db-mysql
    restart: unless-stopped
    container_name: pr-crons
    networks:
      frontend:
        ipv4_address: **********
    environment:
      - ADMIN_HOST=https://party-admin.explain.games
      - AWS_ACCESS_KEY_ID=********************
      - AWS_SECRET_ACCESS_KEY=neGo2ueWhdf87CXnr1q6YWy6XbkSYc2Z2FPYn+gP
      - S3_BUCKET=community.explain.games
      - AWS_REGION=eu-central-1
      - MEMC_HOST=pr-memcached
      - MEMC_PORT=11211
      - MYSQL_HOST=pr-db-mysql
      - MYSQL_DB=party
      - MYSQL_PORT=3306
      - MYSQL_USER=explainer
      - MYSQL_PASS=10IdoSE6umxmu4rPwXsP
      - S3_DEVPATH=dev/
      - PMA_HOST=http://party-pma.explain.games/
      - ISDEV=0
  pr-main:
    build: ./packages/express
    volumes:
      - ./packages/common:/usr/src/common
      - ./packages/express/src:/usr/src/app/src
    networks:
      frontend:
        ipv4_address: **********
    depends_on:
      - pr-api
    container_name: pr-main
    restart: unless-stopped
  pr-memcached:
    image: memcached:1.6.32
    container_name: pr-memcached
    networks:
      frontend:
        ipv4_address: **********
    entrypoint:
      - memcached
      - -m 256
volumes:
  partymysqldb: